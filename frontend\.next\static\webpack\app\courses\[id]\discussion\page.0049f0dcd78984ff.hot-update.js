"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/discussion/page",{

/***/ "(app-pages-browser)/./src/components/DiscussionForum.tsx":
/*!********************************************!*\
  !*** ./src/components/DiscussionForum.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscussionForum)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-pages-browser)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(app-pages-browser)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DiscussionForum(param) {\n    let { courseId, userId } = param;\n    _s();\n    const [discussions, setDiscussions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDiscussion, setSelectedDiscussion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replies, setReplies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewDiscussionForm, setShowNewDiscussionForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReplyForm, setShowReplyForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form states\n    const [newDiscussionTitle, setNewDiscussionTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newDiscussionContent, setNewDiscussionContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newReplyContent, setNewReplyContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Fetch discussions for the course\n    const fetchDiscussions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DiscussionForum.useCallback[fetchDiscussions]\": async ()=>{\n            try {\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/discussions\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setDiscussions(data);\n                }\n            } catch (error) {\n                console.error('Error fetching discussions:', error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"DiscussionForum.useCallback[fetchDiscussions]\"], [\n        courseId\n    ]);\n    // Fetch replies for a specific discussion\n    const fetchReplies = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DiscussionForum.useCallback[fetchReplies]\": async (discussionId)=>{\n            try {\n                const response = await fetch(\"http://localhost:5001/api/discussions/\".concat(discussionId, \"/replies\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setReplies(data);\n                }\n            } catch (error) {\n                console.error('Error fetching replies:', error);\n            }\n        }\n    }[\"DiscussionForum.useCallback[fetchReplies]\"], []);\n    // Create new discussion\n    const createDiscussion = async (e)=>{\n        e.preventDefault();\n        if (!newDiscussionTitle.trim() || !newDiscussionContent.trim() || !userId) return;\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/discussions\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId,\n                    title: newDiscussionTitle,\n                    content: newDiscussionContent\n                })\n            });\n            if (response.ok) {\n                const newDiscussion = await response.json();\n                setDiscussions([\n                    newDiscussion,\n                    ...discussions\n                ]);\n                setNewDiscussionTitle('');\n                setNewDiscussionContent('');\n                setShowNewDiscussionForm(false);\n            }\n        } catch (error) {\n            console.error('Error creating discussion:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Create new reply\n    const createReply = async (e)=>{\n        e.preventDefault();\n        if (!newReplyContent.trim() || !selectedDiscussion || !userId) return;\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"http://localhost:5001/api/discussions/\".concat(selectedDiscussion.id, \"/replies\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId,\n                    content: newReplyContent\n                })\n            });\n            if (response.ok) {\n                const newReply = await response.json();\n                setReplies([\n                    ...replies,\n                    newReply\n                ]);\n                setNewReplyContent('');\n                setShowReplyForm(false);\n                // Update reply count in discussions list\n                setDiscussions(discussions.map((d)=>d.id === selectedDiscussion.id ? {\n                        ...d,\n                        reply_count: d.reply_count + 1\n                    } : d));\n            }\n        } catch (error) {\n            console.error('Error creating reply:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Format date with relative time\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m ago\");\n        if (diffInMinutes < 1440) return \"\".concat(Math.floor(diffInMinutes / 60), \"h ago\");\n        if (diffInMinutes < 10080) return \"\".concat(Math.floor(diffInMinutes / 1440), \"d ago\");\n        return date.toLocaleDateString();\n    };\n    // Get user avatar initials\n    const getUserInitials = (firstName, lastName)=>{\n        return \"\".concat(firstName.charAt(0)).concat(lastName.charAt(0)).toUpperCase();\n    };\n    // Filter discussions based on search\n    const filteredDiscussions = discussions.filter((discussion)=>discussion.title.toLowerCase().includes(searchTerm.toLowerCase()) || discussion.content.toLowerCase().includes(searchTerm.toLowerCase()) || \"\".concat(discussion.first_name, \" \").concat(discussion.last_name).toLowerCase().includes(searchTerm.toLowerCase()));\n    // Handle discussion selection\n    const selectDiscussion = (discussion)=>{\n        setSelectedDiscussion(discussion);\n        setShowReplyForm(false);\n        fetchReplies(discussion.id);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscussionForum.useEffect\": ()=>{\n            if (courseId && userId) {\n                fetchDiscussions();\n            }\n        }\n    }[\"DiscussionForum.useEffect\"], [\n        courseId,\n        userId,\n        fetchDiscussions\n    ]);\n    if (!userId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600 font-medium\",\n                    children: \"Please log in to access discussions\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 font-medium\",\n                        children: \"Loading discussions...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col bg-white rounded-lg shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-gray-200 rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComments,\n                                            className: \"text-white text-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-800\",\n                                                children: \"Discussion Forum\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Connect with other learners, ask questions, and share insights\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowNewDiscussionForm(true),\n                                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faPlus\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Ask a Question\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-6 pt-4 border-t border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComments,\n                                        className: \"text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: discussions.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Discussions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faUsers,\n                                        className: \"text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: new Set(discussions.map((d)=>d.username)).size\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Active Members\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faFire,\n                                        className: \"text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: discussions.reduce((sum, d)=>sum + d.reply_count, 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Total Replies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            showNewDiscussionForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComment,\n                                                className: \"text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: \"Ask a Question\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNewDiscussionForm(false),\n                                    className: \"text-gray-400 hover:text-gray-600 text-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faTimes\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: createDiscussion,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Question Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newDiscussionTitle,\n                                            onChange: (e)=>setNewDiscussionTitle(e.target.value),\n                                            placeholder: \"What's your question about?\",\n                                            className: \"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                            children: \"Question Details *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: newDiscussionContent,\n                                            onChange: (e)=>setNewDiscussionContent(e.target.value),\n                                            placeholder: \"Provide more context about your question. The more details you share, the better answers you'll get!\",\n                                            rows: 6,\n                                            className: \"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 resize-none\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none\",\n                                            children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Posting...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faPaperPlane\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Post Question\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowNewDiscussionForm(false),\n                                            className: \"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white text-lg\",\n                                                children: \"All Discussions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm\",\n                                                children: [\n                                                    filteredDiscussions.length,\n                                                    \" of \",\n                                                    discussions.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faSearch,\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                placeholder: \"Search discussions...\",\n                                                className: \"w-full pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-blue-300 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:bg-opacity-30 focus:border-blue-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto max-h-[500px]\",\n                                children: filteredDiscussions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center text-gray-500\",\n                                    children: searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faSearch,\n                                                className: \"text-4xl mb-3 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    'No discussions found matching \"',\n                                                    searchTerm,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComments,\n                                                className: \"text-4xl mb-3 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No discussions yet. Be the first to ask a question!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this) : filteredDiscussions.map((discussion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>selectDiscussion(discussion),\n                                        className: \"p-5 border-b border-gray-100 cursor-pointer transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 \".concat((selectedDiscussion === null || selectedDiscussion === void 0 ? void 0 : selectedDiscussion.id) === discussion.id ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500 shadow-inner' : ''),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0\",\n                                                    children: getUserInitials(discussion.first_name, discussion.last_name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-2 line-clamp-2\",\n                                                            children: discussion.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                            children: discussion.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                discussion.first_name,\n                                                                                \" \",\n                                                                                discussion.last_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: formatDate(discussion.created_at)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faReply\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 416,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: discussion.reply_count\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faEye\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: Math.floor(Math.random() * 50) + 10\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, discussion.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200\",\n                        children: selectedDiscussion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white text-lg\",\n                                                children: \"Discussion Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-indigo-200 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faReply\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            replies.length,\n                                                            \" replies\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-[500px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border-l-4 border-blue-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-4 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0\",\n                                                                    children: getUserInitials(selectedDiscussion.first_name, selectedDiscussion.last_name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-bold text-gray-800 text-lg mb-2\",\n                                                                            children: selectedDiscussion.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3 text-sm text-gray-600 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: [\n                                                                                        selectedDiscussion.first_name,\n                                                                                        \" \",\n                                                                                        selectedDiscussion.last_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 458,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs\",\n                                                                                    children: \"Original Poster\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 459,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faCalendarAlt\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 462,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: formatDate(selectedDiscussion.created_at)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 463,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 leading-relaxed\",\n                                                            children: selectedDiscussion.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, this),\n                                                replies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"font-semibold text-gray-800 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComments,\n                                                                    className: \"text-indigo-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Replies (\",\n                                                                replies.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        replies.map((reply, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-gray-500 to-gray-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0\",\n                                                                            children: getUserInitials(reply.first_name, reply.last_name)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-semibold text-gray-800\",\n                                                                                            children: [\n                                                                                                reply.first_name,\n                                                                                                \" \",\n                                                                                                reply.last_name\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 486,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"•\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 487,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm text-gray-500\",\n                                                                                            children: formatDate(reply.created_at)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 488,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs\",\n                                                                                            children: [\n                                                                                                \"#\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                            lineNumber: 489,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 485,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-700 leading-relaxed\",\n                                                                                    children: reply.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                                    lineNumber: 491,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, reply.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6\",\n                                            children: showReplyForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: createReply,\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-green-500 to-emerald-600 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faReply\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-gray-700\",\n                                                                children: \"Add your reply\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: newReplyContent,\n                                                        onChange: (e)=>setNewReplyContent(e.target.value),\n                                                        placeholder: \"Share your thoughts, provide an answer, or ask for clarification...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 resize-none\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"submit\",\n                                                                disabled: isSubmitting,\n                                                                className: \"flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-4 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none\",\n                                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"Posting...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faPaperPlane\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"Post Reply\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowReplyForm(false),\n                                                                className: \"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowReplyForm(true),\n                                                className: \"w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white py-4 rounded-xl flex items-center justify-center gap-3 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faReply,\n                                                        className: \"text-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Join the Discussion\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-[500px] text-gray-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-200 to-gray-300 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faComments,\n                                            className: \"text-3xl text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                        children: \"Select a Discussion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a discussion from the left to view details and join the conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\DiscussionForum.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscussionForum, \"G4AGJ4RV59Im8u48sbxipGgzAX0=\");\n_c = DiscussionForum;\nvar _c;\n$RefreshReg$(_c, \"DiscussionForum\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DiscussionForum.tsx\n"));

/***/ })

});