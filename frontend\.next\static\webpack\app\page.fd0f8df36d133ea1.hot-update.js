"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-pages-browser)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(app-pages-browser)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CourseCard = (param)=>{\n    let { course } = param;\n    const getPlaceholderImage = (domain)=>{\n        const domainImages = {\n            'Healthcare & Medicine': '/python-thumb.png',\n            'Insurance & Risk Management': '/react-thumb.png',\n            'Science': '/ui-ux-thumb.png',\n            'Technology': '/react-thumb.png',\n            'Engineering': '/python-thumb.png',\n            'Mathematics': '/ui-ux-thumb.png',\n            'Web Development': '/react-thumb.png',\n            'Data Science': '/python-thumb.png',\n            'Design': '/ui-ux-thumb.png',\n            'Business': '/react-thumb.png',\n            'Finance': '/python-thumb.png'\n        };\n        return domainImages[domain] || '/python-thumb.png'; // Default to available image\n    };\n    const getLevelColor = (level)=>{\n        switch(level.toLowerCase()){\n            case 'beginner':\n                return 'bg-green-100 text-green-700';\n            case 'intermediate':\n                return 'bg-yellow-100 text-yellow-700';\n            case 'advanced':\n                return 'bg-red-100 text-red-700';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-40 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faGraduationCap,\n                                        className: \"text-white text-2xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-700 text-sm font-medium\",\n                                    children: course.domain\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-blue-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full p-3 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faPlay,\n                                    className: \"text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium \".concat(getLevelColor(course.level)),\n                        children: course.level\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-2 line-clamp-2\",\n                        children: course.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                        children: course.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-xs text-gray-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faClock,\n                                            className: \"mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        course.estimated_hours,\n                                        \"h\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faBookOpen,\n                                            className: \"mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Course\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/courses/\".concat(course.id),\n                        className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors duration-200 text-sm font-medium flex items-center justify-center group\",\n                        children: [\n                            \"View Course\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faChevronRight,\n                                className: \"ml-2 text-xs group-hover:translate-x-1 transition-transform duration-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CourseCard;\nfunction Home() {\n    _s();\n    const [coursesByDomain, setCoursesByDomain] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const fetchCoursesByDomain = {\n                \"Home.useEffect.fetchCoursesByDomain\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('http://localhost:5001/api/courses/by-domain');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch courses');\n                        }\n                        const data = await response.json();\n                        setCoursesByDomain(data);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.fetchCoursesByDomain\"];\n            fetchCoursesByDomain();\n        }\n    }[\"Home.useEffect\"], []);\n    const getDomainDescription = (domain)=>{\n        const descriptions = {\n            'Healthcare & Medicine': 'Medical education, healthcare training, and clinical skills',\n            'Insurance & Risk Management': 'Insurance policies, risk assessment, and compliance',\n            'Science': 'Scientific research, laboratory skills, and theoretical knowledge',\n            'Technology': 'Programming, software development, and technical skills',\n            'Engineering': 'Engineering principles, design, and problem-solving',\n            'Mathematics': 'Mathematical concepts, statistics, and analytical thinking',\n            'Web Development': 'Frontend, backend, and full-stack development',\n            'Data Science': 'Data analysis, machine learning, and statistical modeling',\n            'Design': 'UI/UX design, graphic design, and creative skills',\n            'Business': 'Business strategy, management, and entrepreneurship',\n            'Finance': 'Financial analysis, investment, and economic principles'\n        };\n        return descriptions[domain] || 'Comprehensive courses in this field';\n    };\n    const filteredDomains = Object.keys(coursesByDomain).filter((domain)=>{\n        if (!searchQuery) return true;\n        const domainMatches = domain.toLowerCase().includes(searchQuery.toLowerCase());\n        const coursesMatch = coursesByDomain[domain].some((course)=>course.title.toLowerCase().includes(searchQuery.toLowerCase()) || course.description.toLowerCase().includes(searchQuery.toLowerCase()));\n        return domainMatches || coursesMatch;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading courses...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-red-600\",\n                            children: [\n                                \"Error: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-800 mb-4\",\n                                children: \"All Courses by Industry\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-6\",\n                                children: \"Discover courses tailored to your professional field and interests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faSearch,\n                                            className: \"text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search courses...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    filteredDomains.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faSearch,\n                                className: \"text-gray-400 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-medium text-gray-900 mb-2\",\n                                children: \"No courses found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Try adjusting your search terms\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this) : filteredDomains.map((domain)=>{\n                        const courses = coursesByDomain[domain];\n                        const filteredCourses = searchQuery ? courses.filter((course)=>course.title.toLowerCase().includes(searchQuery.toLowerCase()) || course.description.toLowerCase().includes(searchQuery.toLowerCase())) : courses;\n                        if (filteredCourses.length === 0) return null;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                                    children: domain\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: getDomainDescription(domain)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-500 font-medium flex items-center\",\n                                            children: [\n                                                \"Total: \",\n                                                courses.length,\n                                                \" course\",\n                                                courses.length !== 1 ? 's' : ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: filteredCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CourseCard, {\n                                            course: course\n                                        }, course.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, domain, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 15\n                        }, this);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-500 mb-2\",\n                                            children: Object.values(coursesByDomain).flat().length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Total Courses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-500 mb-2\",\n                                            children: Object.keys(coursesByDomain).length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Industry Domains\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-500 mb-2\",\n                                            children: Object.values(coursesByDomain).flat().reduce((sum, course)=>sum + course.estimated_hours, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Learning Hours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"79URfLDLP8ztc1AWuKsNfR6Yk9k=\");\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"CourseCard\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});