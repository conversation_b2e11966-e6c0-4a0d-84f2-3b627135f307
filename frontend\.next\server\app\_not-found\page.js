/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ClientLayout.tsx */ \"(rsc)/./src/app/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/ClientLayout.tsx":
/*!**********************************!*\
  !*** ./src/app/ClientLayout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"141a843e4d49\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHdpbmRvd3N2bTJcXERlc2t0b3BcXENobmFkcnVjb2RlXFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTQxYTg0M2U0ZDQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_400_500_600_700_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"variable\":\"--font-poppins\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-poppins\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_400_500_600_700_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_400_500_600_700_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fontawesome */ \"(rsc)/./src/lib/fontawesome.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ClientLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClientLayout */ \"(rsc)/./src/app/ClientLayout.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_400_500_600_700_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/fontawesome.ts":
/*!********************************!*\
  !*** ./src/lib/fontawesome.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n\n\n// Tell Font Awesome to skip adding the CSS automatically \n// since it's already imported above\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_0__.config.autoAddCss = false;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ZvbnRhd2Vzb21lLnRzIiwibWFwcGluZ3MiOiI7OztBQUEyRDtBQUNMO0FBRXRELDBEQUEwRDtBQUMxRCxvQ0FBb0M7QUFDcENBLHFFQUFNQSxDQUFDQyxVQUFVLEdBQUciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcd2luZG93c3ZtMlxcRGVza3RvcFxcQ2huYWRydWNvZGVcXGZyb250ZW5kXFxzcmNcXGxpYlxcZm9udGF3ZXNvbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29uZmlnIH0gZnJvbSAnQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlJztcbmltcG9ydCAnQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlL3N0eWxlcy5jc3MnO1xuXG4vLyBUZWxsIEZvbnQgQXdlc29tZSB0byBza2lwIGFkZGluZyB0aGUgQ1NTIGF1dG9tYXRpY2FsbHkgXG4vLyBzaW5jZSBpdCdzIGFscmVhZHkgaW1wb3J0ZWQgYWJvdmVcbmNvbmZpZy5hdXRvQWRkQ3NzID0gZmFsc2U7XG4iXSwibmFtZXMiOlsiY29uZmlnIiwiYXV0b0FkZENzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/fontawesome.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ClientLayout.tsx */ \"(ssr)/./src/app/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cwindowsvm2%5C%5CDesktop%5C%5CChnadrucode%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/ClientLayout.tsx":
/*!**********************************!*\
  !*** ./src/app/ClientLayout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layouts_SideNav_SideBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layouts/SideNav/SideBar */ \"(ssr)/./src/components/Layouts/SideNav/SideBar.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/NotificationContext */ \"(ssr)/./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _components_Layouts_Header_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layouts/Header/page */ \"(ssr)/./src/components/Layouts/Header/page.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ClientLayout({ children }) {\n    const [sidebarExpanded, setSidebarExpanded] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const hideLayoutPaths = [\n        \"/\",\n        \"/courses\",\n        \"/courses/\",\n        \"/signin\",\n        \"/signin/\",\n        \"/signup\",\n        \"/signup/\",\n        \"/forgot-password\",\n        \"/forgot-password/\",\n        \"/my-dashboard\",\n        \"/my-dashboard/\",\n        \"/my-learning\",\n        \"/my-learning/\",\n        \"/my-certificates\",\n        \"/my-certificates/\"\n    ];\n    // Check for exact matches or dynamic course routes\n    const hideLayout = hideLayoutPaths.includes(pathname) || pathname.startsWith(\"/courses/\") && pathname.match(/^\\/courses\\/\\d+\\/?$/);\n    if (hideLayout) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n            lineNumber: 30,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_SideNav_SideBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sidebarExpanded: sidebarExpanded,\n                setSidebarExpanded: setSidebarExpanded\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_Header_page__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sidebarExpanded: sidebarExpanded\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/Header/page.tsx":
/*!************************************************!*\
  !*** ./src/components/Layouts/Header/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(ssr)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(ssr)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _components_NotificationDropdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/NotificationDropdown */ \"(ssr)/./src/components/NotificationDropdown.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=GraduationCap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Navbar = ({ sidebarExpanded })=>{\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // Function to decode JWT token\n    const decodeJWT = (token)=>{\n        try {\n            const base64Url = token.split(\".\")[1];\n            const base64 = base64Url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n            const jsonPayload = decodeURIComponent(atob(base64).split(\"\").map(function(c) {\n                return \"%\" + (\"00\" + c.charCodeAt(0).toString(16)).slice(-2);\n            }).join(\"\"));\n            return JSON.parse(jsonPayload);\n        } catch (error) {\n            console.error(\"Error decoding JWT:\", error);\n            return null;\n        }\n    };\n    // Get user data from token\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                const decodedToken = decodeJWT(token);\n                if (decodedToken) {\n                    setUser({\n                        id: decodedToken.id,\n                        email: decodedToken.email,\n                        username: decodedToken.username,\n                        role: decodedToken.role,\n                        first_name: decodedToken.first_name || decodedToken.username,\n                        last_name: decodedToken.last_name || \"\"\n                    });\n                }\n            }\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Generate user initials\n    const getUserInitials = (firstName, lastName)=>{\n        const first = firstName ? firstName.charAt(0).toUpperCase() : \"\";\n        const last = lastName ? lastName.charAt(0).toUpperCase() : \"\";\n        return first + last || \"U\";\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Navbar.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsDropdownOpen(false);\n                    }\n                }\n            }[\"Navbar.useEffect.handleClickOutside\"];\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        sessionStorage.removeItem(\"token\");\n        localStorage.removeItem(\"authToken\");\n        sessionStorage.removeItem(\"authToken\");\n        setUser(null);\n        router.push(\"/signin\");\n    };\n    const toggleDropdown = ()=>{\n        setIsDropdownOpen(!isDropdownOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b sticky top-0 z-30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex justify-between items-center py-1 px-4\",\n            children: [\n                !sidebarExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 absolute left-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-orange-500 text-lg font-bold\",\n                                    children: \"EX\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#005071] text-lg font-bold\",\n                                    children: \"Learn\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow flex justify-center mx-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-xs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationDropdown__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            ref: dropdownRef,\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"flex items-center space-x-2 px-2 py-1 rounded-full hover:bg-orange-50\",\n                                        onClick: toggleDropdown,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium\",\n                                                children: getUserInitials(user.first_name, user.last_name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 text-sm font-medium\",\n                                                children: user.first_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronDown,\n                                                className: `text-gray-500 text-xs transition-transform duration-200 ${isDropdownOpen ? \"rotate-180\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-3 border-b border-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium\",\n                                                            children: getUserInitials(user.first_name, user.last_name)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: [\n                                                                        user.first_name,\n                                                                        \" \",\n                                                                        user.last_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"ghost\",\n                                                        className: \"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50\",\n                                                        onClick: ()=>{\n                                                            setIsDropdownOpen(false);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faUser,\n                                                                className: \"mr-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"Profile\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"ghost\",\n                                                        className: \"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                                        onClick: handleLogout,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faSignOutAlt,\n                                                                className: \"mr-3 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"Log out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                asChild: true,\n                                className: \"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/signin\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faUser,\n                                            className: \"text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\Header\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/Header/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/SideNav/SideBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/Layouts/SideNav/SideBar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,GraduationCap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,GraduationCap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,GraduationCap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_Layouts_SideNav_SidebarLinks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layouts/SideNav/SidebarLinks */ \"(ssr)/./src/components/Layouts/SideNav/SidebarLinks.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ sidebarExpanded, setSidebarExpanded, activePanel, setActivePanel }) {\n    const navItems = _components_Layouts_SideNav_SidebarLinks__WEBPACK_IMPORTED_MODULE_3__.sidebarLinks;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `bg-white border-r border-gray-200 shadow-sm transition-all duration-300 ease-in-out flex flex-col ${sidebarExpanded ? \"w-48\" : \"w-16\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `py-2 px-4 border-b border-gray-200 flex items-center ${sidebarExpanded ? \"justify-between\" : \"justify-center\"}`,\n                children: [\n                    sidebarExpanded && // <div className=\"flex items-center gap-3\">\n                    //   <div className=\"w-8 h-8 bg-gradient-to-br from-orange-700 to-purple-500 rounded-lg flex items-center justify-center\">\n                    //     <span className=\"text-white font-bold text-xs\">AiT</span>\n                    //   </div>\n                    //   <span className=\"font-bold text-sm tracking-tight text-orange-400\">AI Trainer</span>\n                    // </div>\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-500 text-lg font-bold\",\n                                        children: \"EX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#005071] text-lg font-bold\",\n                                        children: \"Learn\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>setSidebarExpanded(!sidebarExpanded),\n                        className: \"p-2 hover:bg-gray-100\",\n                        \"aria-label\": sidebarExpanded ? \"Collapse sidebar\" : \"Expand sidebar\",\n                        children: sidebarExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 19\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_GraduationCap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: item.href,\n                            className: `flex items-center gap-3 py-1.5 px-3 rounded-lg text-[#005071] hover:text-white hover:bg-[#005071] transition-colors group ${sidebarExpanded ? \"justify-start\" : \"justify-center\"}`,\n                            title: !sidebarExpanded ? item.label : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"transition-colors\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 21\n                                }, this),\n                                sidebarExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium\",\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 23\n                                }, this)\n                            ]\n                        }, item.href, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 19\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 15\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n                lineNumber: 67,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SideBar.tsx\",\n        lineNumber: 23,\n        columnNumber: 11\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/SideNav/SideBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layouts/SideNav/SidebarLinks.tsx":
/*!*********************************************************!*\
  !*** ./src/components/Layouts/SideNav/SidebarLinks.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sidebarLinks: () => (/* binding */ sidebarLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Layers,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Layers,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Layers,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Layers,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n'use-client';\n\n\nconst sidebarLinks = [\n    // {\n    //   label: \"Home\",\n    //   icon: <Home className=\"w-5 h-5\" />,\n    //   href: \"/\", // or your actual route\n    // },\n    // {\n    //   label: \"Dashboard\",\n    //   icon: <LayoutDashboard className=\"w-5 h-5\" />,\n    //   href: \"/my-dashboard\", // or your actual route\n    // },\n    // {\n    //   label: \"Learning\",\n    //   icon: <GraduationCap className=\"w-5 h-5\" />,\n    //   href: \"/my-learning\", // or your actual route\n    // },\n    // {\n    //   label: \"Certificate\",\n    //   icon: <ShieldCheck className=\"w-5 h-5\" />,\n    //   href: \"/my-certificates\", // or your actual route\n    // },\n    {\n        label: \"Course Builder\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SidebarLinks.tsx\",\n            lineNumber: 40,\n            columnNumber: 11\n        }, undefined),\n        href: \"/admin/upload\"\n    },\n    {\n        label: \"AI Summarization\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SidebarLinks.tsx\",\n            lineNumber: 45,\n            columnNumber: 11\n        }, undefined),\n        href: \"/admin/ai-summarization\"\n    },\n    {\n        label: \"Avatar & Video\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SidebarLinks.tsx\",\n            lineNumber: 50,\n            columnNumber: 11\n        }, undefined),\n        href: \"/admin/avatar-video\"\n    },\n    {\n        label: \"Course Builder\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Layers_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\Layouts\\\\SideNav\\\\SidebarLinks.tsx\",\n            lineNumber: 55,\n            columnNumber: 11\n        }, undefined),\n        href: \"/admin/course-builder\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layouts/SideNav/SidebarLinks.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NotificationDropdown.tsx":
/*!*************************************************!*\
  !*** ./src/components/NotificationDropdown.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(ssr)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(ssr)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/NotificationContext */ \"(ssr)/./src/contexts/NotificationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst NotificationDropdown = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { notifications, unreadCount, markAsRead, markAllAsRead, clearNotifications } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.useNotifications)();\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationDropdown.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationDropdown.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationDropdown.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationDropdown.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"NotificationDropdown.useEffect\"];\n        }\n    }[\"NotificationDropdown.useEffect\"], []);\n    const toggleDropdown = ()=>{\n        setIsOpen(!isOpen);\n    };\n    const handleNotificationClick = (notificationId, courseId)=>{\n        markAsRead(notificationId);\n        // Navigate to course (you can implement navigation logic here)\n        window.location.href = `/courses/${courseId}`;\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const notificationTime = new Date(timestamp);\n        const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;\n        return `${Math.floor(diffInMinutes / 1440)}d ago`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative cursor-pointer hover:bg-gray-100 rounded-full p-2 transition-colors duration-200\",\n                onClick: toggleDropdown,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faBell,\n                        className: \"text-gray-600 text-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, undefined),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\",\n                        children: unreadCount > 99 ? '99+' : unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-gray-100 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800\",\n                                children: \"Notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: markAllAsRead,\n                                        className: \"text-xs text-blue-600 hover:text-blue-800 flex items-center\",\n                                        title: \"Mark all as read\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faCheck,\n                                                className: \"mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Mark all read\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearNotifications,\n                                        className: \"text-xs text-gray-500 hover:text-gray-700\",\n                                        title: \"Clear all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faTimes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-80 overflow-y-auto\",\n                        children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-8 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faBell,\n                                    className: \"text-3xl mb-2 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 15\n                        }, undefined) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `px-4 py-3 border-b border-gray-50 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${!notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''}`,\n                                onClick: ()=>handleNotificationClick(notification.id, notification.courseId),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faGraduationCap,\n                                                    className: \"text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: notification.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                            children: notification.domain\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: formatTimeAgo(notification.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined),\n                    notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 border-t border-gray-100 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"View All Notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\NotificationDropdown.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationDropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NotificationDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"underline-offset-4 hover:underline text-primary\",\n            brand: \"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80\",\n            sucess: \"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80\",\n            warning: \"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80\"\n        },\n        size: {\n            default: \"h-10 py-2 px-4\",\n            sm: \"h-9 px-3 rounded-md\",\n            lg: \"h-11 px-8 rounded-md\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationProvider,useNotifications auto */ \n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst NotificationProvider = ({ children })=>{\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationProvider.useEffect\": ()=>{\n            // Initialize socket connection\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:5001\" + '', {\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                autoConnect: true\n            });\n            setSocket(newSocket);\n            // Connection event handlers\n            newSocket.on('connect', {\n                \"NotificationProvider.useEffect\": ()=>{\n                    console.log('Connected to notification server');\n                    setIsConnected(true);\n                    // Join user room if authenticated\n                    const token = localStorage.getItem('token');\n                    if (token) {\n                        try {\n                            const decodedToken = JSON.parse(atob(token.split('.')[1]));\n                            newSocket.emit('join_user', decodedToken.id);\n                        } catch (error) {\n                            console.error('Error decoding token:', error);\n                        }\n                    }\n                }\n            }[\"NotificationProvider.useEffect\"]);\n            newSocket.on('disconnect', {\n                \"NotificationProvider.useEffect\": ()=>{\n                    console.log('Disconnected from notification server');\n                    setIsConnected(false);\n                }\n            }[\"NotificationProvider.useEffect\"]);\n            // Listen for new notifications\n            newSocket.on('new_notification', {\n                \"NotificationProvider.useEffect\": (notification)=>{\n                    console.log('New notification received:', notification);\n                    setNotifications({\n                        \"NotificationProvider.useEffect\": (prev)=>[\n                                notification,\n                                ...prev\n                            ]\n                    }[\"NotificationProvider.useEffect\"]);\n                    // Show browser notification if permission granted\n                    if (Notification.permission === 'granted') {\n                        new Notification(notification.title, {\n                            body: notification.message,\n                            icon: '/favicon.ico'\n                        });\n                    }\n                }\n            }[\"NotificationProvider.useEffect\"]);\n            // Request notification permission\n            if (Notification.permission === 'default') {\n                Notification.requestPermission();\n            }\n            // Fetch existing notifications on mount\n            fetchNotifications();\n            // Cleanup on unmount\n            return ({\n                \"NotificationProvider.useEffect\": ()=>{\n                    newSocket.close();\n                }\n            })[\"NotificationProvider.useEffect\"];\n        }\n    }[\"NotificationProvider.useEffect\"], []);\n    const fetchNotifications = async ()=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) return;\n            const response = await fetch(\"http://localhost:5001\" + '/api/notifications', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setNotifications(data);\n            }\n        } catch (error) {\n            console.error('Error fetching notifications:', error);\n        }\n    };\n    const markAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    read: true\n                })));\n    };\n    const clearNotifications = ()=>{\n        setNotifications([]);\n    };\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const value = {\n        notifications,\n        unreadCount,\n        isConnected,\n        markAsRead,\n        markAllAsRead,\n        clearNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error('useNotifications must be used within a NotificationProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvTm90aWZpY2F0aW9uQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUY7QUFDM0M7QUF1QjlDLE1BQU1NLG9DQUFzQkwsb0RBQWFBLENBQXNDTTtBQU14RSxNQUFNQyx1QkFBNEQsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDcEYsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR1AsK0NBQVFBLENBQWlCLEVBQUU7SUFDckUsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ1UsUUFBUUMsVUFBVSxHQUFHWCwrQ0FBUUEsQ0FBZ0I7SUFFcERELGdEQUFTQTswQ0FBQztZQUNSLCtCQUErQjtZQUMvQixNQUFNYSxZQUFZWCxvREFBRUEsQ0FBQ1ksdUJBQWlDLEdBQUMsSUFBSTtnQkFDekRHLFlBQVk7b0JBQUM7b0JBQWE7aUJBQVU7Z0JBQ3BDQyxhQUFhO1lBQ2Y7WUFFQU4sVUFBVUM7WUFFViw0QkFBNEI7WUFDNUJBLFVBQVVNLEVBQUUsQ0FBQztrREFBVztvQkFDdEJDLFFBQVFDLEdBQUcsQ0FBQztvQkFDWlgsZUFBZTtvQkFFZixrQ0FBa0M7b0JBQ2xDLE1BQU1ZLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztvQkFDbkMsSUFBSUYsT0FBTzt3QkFDVCxJQUFJOzRCQUNGLE1BQU1HLGVBQWVDLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS04sTUFBTU8sS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFOzRCQUN4RGhCLFVBQVVpQixJQUFJLENBQUMsYUFBYUwsYUFBYU0sRUFBRTt3QkFDN0MsRUFBRSxPQUFPQyxPQUFPOzRCQUNkWixRQUFRWSxLQUFLLENBQUMseUJBQXlCQTt3QkFDekM7b0JBQ0Y7Z0JBQ0Y7O1lBRUFuQixVQUFVTSxFQUFFLENBQUM7a0RBQWM7b0JBQ3pCQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1pYLGVBQWU7Z0JBQ2pCOztZQUVBLCtCQUErQjtZQUMvQkcsVUFBVU0sRUFBRSxDQUFDO2tEQUFvQixDQUFDYztvQkFDaENiLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJZO29CQUMxQ3pCOzBEQUFpQjBCLENBQUFBLE9BQVE7Z0NBQUNEO21DQUFpQkM7NkJBQUs7O29CQUVoRCxrREFBa0Q7b0JBQ2xELElBQUlDLGFBQWFDLFVBQVUsS0FBSyxXQUFXO3dCQUN6QyxJQUFJRCxhQUFhRixhQUFhSSxLQUFLLEVBQUU7NEJBQ25DQyxNQUFNTCxhQUFhTSxPQUFPOzRCQUMxQkMsTUFBTTt3QkFDUjtvQkFDRjtnQkFDRjs7WUFFQSxrQ0FBa0M7WUFDbEMsSUFBSUwsYUFBYUMsVUFBVSxLQUFLLFdBQVc7Z0JBQ3pDRCxhQUFhTSxpQkFBaUI7WUFDaEM7WUFFQSx3Q0FBd0M7WUFDeENDO1lBRUEscUJBQXFCO1lBQ3JCO2tEQUFPO29CQUNMN0IsVUFBVThCLEtBQUs7Z0JBQ2pCOztRQUNGO3lDQUFHLEVBQUU7SUFFTCxNQUFNRCxxQkFBcUI7UUFDekIsSUFBSTtZQUNGLE1BQU1wQixRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsSUFBSSxDQUFDRixPQUFPO1lBRVosTUFBTXNCLFdBQVcsTUFBTUMsTUFBTS9CLHVCQUFpQyxHQUFDLHNCQUFzQjtnQkFDbkZnQyxTQUFTO29CQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRXhCLE9BQU87b0JBQ2xDLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUlzQixTQUFTRyxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSixTQUFTSyxJQUFJO2dCQUNoQ3pDLGlCQUFpQndDO1lBQ25CO1FBQ0YsRUFBRSxPQUFPaEIsT0FBTztZQUNkWixRQUFRWSxLQUFLLENBQUMsaUNBQWlDQTtRQUNqRDtJQUNGO0lBRUEsTUFBTWtCLGFBQWEsQ0FBQ0M7UUFDbEIzQyxpQkFBaUIwQixDQUFBQSxPQUNmQSxLQUFLa0IsR0FBRyxDQUFDbkIsQ0FBQUEsZUFDUEEsYUFBYUYsRUFBRSxLQUFLb0IsaUJBQ2hCO29CQUFFLEdBQUdsQixZQUFZO29CQUFFb0IsTUFBTTtnQkFBSyxJQUM5QnBCO0lBR1Y7SUFFQSxNQUFNcUIsZ0JBQWdCO1FBQ3BCOUMsaUJBQWlCMEIsQ0FBQUEsT0FDZkEsS0FBS2tCLEdBQUcsQ0FBQ25CLENBQUFBLGVBQWlCO29CQUFFLEdBQUdBLFlBQVk7b0JBQUVvQixNQUFNO2dCQUFLO0lBRTVEO0lBRUEsTUFBTUUscUJBQXFCO1FBQ3pCL0MsaUJBQWlCLEVBQUU7SUFDckI7SUFFQSxNQUFNZ0QsY0FBY2pELGNBQWNrRCxNQUFNLENBQUNDLENBQUFBLElBQUssQ0FBQ0EsRUFBRUwsSUFBSSxFQUFFTSxNQUFNO0lBRTdELE1BQU1DLFFBQWlDO1FBQ3JDckQ7UUFDQWlEO1FBQ0EvQztRQUNBeUM7UUFDQUk7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDcEQsb0JBQW9CMEQsUUFBUTtRQUFDRCxPQUFPQTtrQkFDbEN0RDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU13RCxtQkFBbUI7SUFDOUIsTUFBTUMsVUFBVWhFLGlEQUFVQSxDQUFDSTtJQUMzQixJQUFJNEQsWUFBWTNELFdBQVc7UUFDekIsTUFBTSxJQUFJNEQsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx3aW5kb3dzdm0yXFxEZXNrdG9wXFxDaG5hZHJ1Y29kZVxcZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXE5vdGlmaWNhdGlvbkNvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGlvLCBTb2NrZXQgfSBmcm9tICdzb2NrZXQuaW8tY2xpZW50JztcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBjb3Vyc2VJZDogbnVtYmVyO1xuICBjb3Vyc2VUaXRsZTogc3RyaW5nO1xuICBkb21haW46IHN0cmluZztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIHJlYWQ6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25Db250ZXh0VHlwZSB7XG4gIG5vdGlmaWNhdGlvbnM6IE5vdGlmaWNhdGlvbltdO1xuICB1bnJlYWRDb3VudDogbnVtYmVyO1xuICBpc0Nvbm5lY3RlZDogYm9vbGVhbjtcbiAgbWFya0FzUmVhZDogKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG1hcmtBbGxBc1JlYWQ6ICgpID0+IHZvaWQ7XG4gIGNsZWFyTm90aWZpY2F0aW9uczogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgTm90aWZpY2F0aW9uQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8Tm90aWZpY2F0aW9uQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IE5vdGlmaWNhdGlvblByb3ZpZGVyOiBSZWFjdC5GQzxOb3RpZmljYXRpb25Qcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW25vdGlmaWNhdGlvbnMsIHNldE5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8Tm90aWZpY2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW2lzQ29ubmVjdGVkLCBzZXRJc0Nvbm5lY3RlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzb2NrZXQsIHNldFNvY2tldF0gPSB1c2VTdGF0ZTxTb2NrZXQgfCBudWxsPihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgc29ja2V0IGNvbm5lY3Rpb25cbiAgICBjb25zdCBuZXdTb2NrZXQgPSBpbyhwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19FTkRfUE9JTlQrJycsIHtcbiAgICAgIHRyYW5zcG9ydHM6IFsnd2Vic29ja2V0JywgJ3BvbGxpbmcnXSxcbiAgICAgIGF1dG9Db25uZWN0OiB0cnVlLFxuICAgIH0pO1xuXG4gICAgc2V0U29ja2V0KG5ld1NvY2tldCk7XG5cbiAgICAvLyBDb25uZWN0aW9uIGV2ZW50IGhhbmRsZXJzXG4gICAgbmV3U29ja2V0Lm9uKCdjb25uZWN0JywgKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ0Nvbm5lY3RlZCB0byBub3RpZmljYXRpb24gc2VydmVyJyk7XG4gICAgICBzZXRJc0Nvbm5lY3RlZCh0cnVlKTtcbiAgICAgIFxuICAgICAgLy8gSm9pbiB1c2VyIHJvb20gaWYgYXV0aGVudGljYXRlZFxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGlmICh0b2tlbikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGRlY29kZWRUb2tlbiA9IEpTT04ucGFyc2UoYXRvYih0b2tlbi5zcGxpdCgnLicpWzFdKSk7XG4gICAgICAgICAgbmV3U29ja2V0LmVtaXQoJ2pvaW5fdXNlcicsIGRlY29kZWRUb2tlbi5pZCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVjb2RpbmcgdG9rZW46JywgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBuZXdTb2NrZXQub24oJ2Rpc2Nvbm5lY3QnLCAoKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnRGlzY29ubmVjdGVkIGZyb20gbm90aWZpY2F0aW9uIHNlcnZlcicpO1xuICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xuICAgIH0pO1xuXG4gICAgLy8gTGlzdGVuIGZvciBuZXcgbm90aWZpY2F0aW9uc1xuICAgIG5ld1NvY2tldC5vbignbmV3X25vdGlmaWNhdGlvbicsIChub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbikgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ05ldyBub3RpZmljYXRpb24gcmVjZWl2ZWQ6Jywgbm90aWZpY2F0aW9uKTtcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMocHJldiA9PiBbbm90aWZpY2F0aW9uLCAuLi5wcmV2XSk7XG4gICAgICBcbiAgICAgIC8vIFNob3cgYnJvd3NlciBub3RpZmljYXRpb24gaWYgcGVybWlzc2lvbiBncmFudGVkXG4gICAgICBpZiAoTm90aWZpY2F0aW9uLnBlcm1pc3Npb24gPT09ICdncmFudGVkJykge1xuICAgICAgICBuZXcgTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbi50aXRsZSwge1xuICAgICAgICAgIGJvZHk6IG5vdGlmaWNhdGlvbi5tZXNzYWdlLFxuICAgICAgICAgIGljb246ICcvZmF2aWNvbi5pY28nLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFJlcXVlc3Qgbm90aWZpY2F0aW9uIHBlcm1pc3Npb25cbiAgICBpZiAoTm90aWZpY2F0aW9uLnBlcm1pc3Npb24gPT09ICdkZWZhdWx0Jykge1xuICAgICAgTm90aWZpY2F0aW9uLnJlcXVlc3RQZXJtaXNzaW9uKCk7XG4gICAgfVxuXG4gICAgLy8gRmV0Y2ggZXhpc3Rpbmcgbm90aWZpY2F0aW9ucyBvbiBtb3VudFxuICAgIGZldGNoTm90aWZpY2F0aW9ucygpO1xuXG4gICAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG5ld1NvY2tldC5jbG9zZSgpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICBjb25zdCBmZXRjaE5vdGlmaWNhdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBpZiAoIXRva2VuKSByZXR1cm47XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU5EX1BPSU5UKycvYXBpL25vdGlmaWNhdGlvbnMnLCB7XG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldE5vdGlmaWNhdGlvbnMoZGF0YSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBtYXJrQXNSZWFkID0gKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpID0+IHtcbiAgICBzZXROb3RpZmljYXRpb25zKHByZXYgPT5cbiAgICAgIHByZXYubWFwKG5vdGlmaWNhdGlvbiA9PlxuICAgICAgICBub3RpZmljYXRpb24uaWQgPT09IG5vdGlmaWNhdGlvbklkXG4gICAgICAgICAgPyB7IC4uLm5vdGlmaWNhdGlvbiwgcmVhZDogdHJ1ZSB9XG4gICAgICAgICAgOiBub3RpZmljYXRpb25cbiAgICAgIClcbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IG1hcmtBbGxBc1JlYWQgPSAoKSA9PiB7XG4gICAgc2V0Tm90aWZpY2F0aW9ucyhwcmV2ID0+XG4gICAgICBwcmV2Lm1hcChub3RpZmljYXRpb24gPT4gKHsgLi4ubm90aWZpY2F0aW9uLCByZWFkOiB0cnVlIH0pKVxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgY2xlYXJOb3RpZmljYXRpb25zID0gKCkgPT4ge1xuICAgIHNldE5vdGlmaWNhdGlvbnMoW10pO1xuICB9O1xuXG4gIGNvbnN0IHVucmVhZENvdW50ID0gbm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiAhbi5yZWFkKS5sZW5ndGg7XG5cbiAgY29uc3QgdmFsdWU6IE5vdGlmaWNhdGlvbkNvbnRleHRUeXBlID0ge1xuICAgIG5vdGlmaWNhdGlvbnMsXG4gICAgdW5yZWFkQ291bnQsXG4gICAgaXNDb25uZWN0ZWQsXG4gICAgbWFya0FzUmVhZCxcbiAgICBtYXJrQWxsQXNSZWFkLFxuICAgIGNsZWFyTm90aWZpY2F0aW9ucyxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxOb3RpZmljYXRpb25Db250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Ob3RpZmljYXRpb25Db250ZXh0LlByb3ZpZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZU5vdGlmaWNhdGlvbnMgPSAoKTogTm90aWZpY2F0aW9uQ29udGV4dFR5cGUgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChOb3RpZmljYXRpb25Db250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlTm90aWZpY2F0aW9ucyBtdXN0IGJlIHVzZWQgd2l0aGluIGEgTm90aWZpY2F0aW9uUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiaW8iLCJOb3RpZmljYXRpb25Db250ZXh0IiwidW5kZWZpbmVkIiwiTm90aWZpY2F0aW9uUHJvdmlkZXIiLCJjaGlsZHJlbiIsIm5vdGlmaWNhdGlvbnMiLCJzZXROb3RpZmljYXRpb25zIiwiaXNDb25uZWN0ZWQiLCJzZXRJc0Nvbm5lY3RlZCIsInNvY2tldCIsInNldFNvY2tldCIsIm5ld1NvY2tldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19FTkRfUE9JTlQiLCJ0cmFuc3BvcnRzIiwiYXV0b0Nvbm5lY3QiLCJvbiIsImNvbnNvbGUiLCJsb2ciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJkZWNvZGVkVG9rZW4iLCJKU09OIiwicGFyc2UiLCJhdG9iIiwic3BsaXQiLCJlbWl0IiwiaWQiLCJlcnJvciIsIm5vdGlmaWNhdGlvbiIsInByZXYiLCJOb3RpZmljYXRpb24iLCJwZXJtaXNzaW9uIiwidGl0bGUiLCJib2R5IiwibWVzc2FnZSIsImljb24iLCJyZXF1ZXN0UGVybWlzc2lvbiIsImZldGNoTm90aWZpY2F0aW9ucyIsImNsb3NlIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJvayIsImRhdGEiLCJqc29uIiwibWFya0FzUmVhZCIsIm5vdGlmaWNhdGlvbklkIiwibWFwIiwicmVhZCIsIm1hcmtBbGxBc1JlYWQiLCJjbGVhck5vdGlmaWNhdGlvbnMiLCJ1bnJlYWRDb3VudCIsImZpbHRlciIsIm4iLCJsZW5ndGgiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlTm90aWZpY2F0aW9ucyIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx3aW5kb3dzdm0yXFxEZXNrdG9wXFxDaG5hZHJ1Y29kZVxcZnJvbnRlbmRcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@fortawesome","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/prop-types","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/engine.io-parser","vendor-chunks/lucide-react","vendor-chunks/react-is","vendor-chunks/@radix-ui","vendor-chunks/@socket.io","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwindowsvm2%5CDesktop%5CChnadrucode%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();