"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/discussion/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/courses/[id]/discussion/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscussionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_DiscussionForum__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/DiscussionForum */ \"(app-pages-browser)/./src/components/DiscussionForum.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DiscussionPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const courseId = params.id;\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        console.log('Discussion page - Token from localStorage:', token);\n        // Also check for user data directly\n        const userData = localStorage.getItem('user');\n        console.log('Discussion page - User data from localStorage:', userData);\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        // Fallback to user data if token doesn't work\n        if (userData) {\n            try {\n                return JSON.parse(userData);\n            } catch (error) {\n                console.error('Error parsing user data:', error);\n            }\n        }\n        return null;\n    };\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch course details\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscussionPage.useEffect\": ()=>{\n            const fetchCourse = {\n                \"DiscussionPage.useEffect.fetchCourse\": async ()=>{\n                    try {\n                        const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (response.ok) {\n                            const courseData = await response.json();\n                            setCourse(courseData);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching course:', error);\n                    }\n                }\n            }[\"DiscussionPage.useEffect.fetchCourse\"];\n            if (courseId) {\n                fetchCourse();\n            }\n        }\n    }[\"DiscussionPage.useEffect\"], [\n        courseId\n    ]);\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscussionPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            console.log('Discussion page - User from token:', user);\n            if (user) {\n                setCurrentUser(user);\n                console.log('Discussion page - User set:', user);\n            } else {\n                console.log('Discussion page - No valid token');\n            // Don't redirect immediately, let the user see the login message\n            }\n            setLoading(false);\n        }\n    }[\"DiscussionPage.useEffect\"], [\n        router\n    ]);\n    const handleBackToCourse = ()=>{\n        router.push(\"/courses/\".concat(courseId));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Loading discussion forum...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleBackToCourse,\n                                        className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Course\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    course && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-px bg-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Discussion Forum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                        children: course === null || course === void 0 ? void 0 : course.domain\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                        children: course === null || course === void 0 ? void 0 : course.level\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: [\n                        console.log('Discussion page render - currentUser:', currentUser, 'userId:', userId),\n                        userId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiscussionForum__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            courseId: courseId,\n                            userId: userId\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Login Required\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-6\",\n                                        children: \"Please log in to access the discussion forum and connect with other learners.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/login'),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Go to Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscussionPage, \"W/mWipC78zh17Q3Nd3kRtYdPUDY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DiscussionPage;\nvar _c;\n$RefreshReg$(_c, \"DiscussionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx\n"));

/***/ })

});