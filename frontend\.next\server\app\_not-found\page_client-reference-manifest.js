globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.tsx":{"*":{"id":"(ssr)/./src/app/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/page.tsx":{"*":{"id":"(ssr)/./src/app/courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/courses/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/signin/page.tsx":{"*":{"id":"(ssr)/./src/app/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/upload/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/upload/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/ai-summarization/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/ai-summarization/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/avatar-video/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/avatar-video/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/course-builder/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/course-builder/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/my-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-learning/page.tsx":{"*":{"id":"(ssr)/./src/app/my-learning/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-certificates/page.tsx":{"*":{"id":"(ssr)/./src/app/my-certificates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx":{"*":{"id":"(ssr)/./src/app/courses/[id]/discussion/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"variable\":\"--font-poppins\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"variable\":\"--font-poppins\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\ClientLayout.tsx":{"id":"(app-pages-browser)/./src/app/ClientLayout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\@fortawesome\\fontawesome-svg-core\\styles.css":{"id":"(app-pages-browser)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\courses\\page.tsx":{"id":"(app-pages-browser)/./src/app/courses/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\courses\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/courses/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\signin\\page.tsx":{"id":"(app-pages-browser)/./src/app/signin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\admin\\upload\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/upload/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\admin\\ai-summarization\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/ai-summarization/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\admin\\avatar-video\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/avatar-video/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\admin\\course-builder\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/course-builder/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\my-dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/my-dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\my-learning\\page.tsx":{"id":"(app-pages-browser)/./src/app/my-learning/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\my-certificates\\page.tsx":{"id":"(app-pages-browser)/./src/app/my-certificates/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\courses\\[id]\\discussion\\page.tsx":{"id":"(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Chnadrucode\\frontend\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.tsx":{"*":{"id":"(rsc)/./src/app/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css":{"*":{"id":"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/page.tsx":{"*":{"id":"(rsc)/./src/app/courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/courses/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/signin/page.tsx":{"*":{"id":"(rsc)/./src/app/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/upload/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/upload/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/ai-summarization/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/ai-summarization/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/avatar-video/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/avatar-video/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/course-builder/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/course-builder/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/my-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-learning/page.tsx":{"*":{"id":"(rsc)/./src/app/my-learning/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/my-certificates/page.tsx":{"*":{"id":"(rsc)/./src/app/my-certificates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx":{"*":{"id":"(rsc)/./src/app/courses/[id]/discussion/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}