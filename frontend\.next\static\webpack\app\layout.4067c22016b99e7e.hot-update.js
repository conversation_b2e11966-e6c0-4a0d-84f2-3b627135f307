"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/ClientLayout.tsx":
/*!**********************************!*\
  !*** ./src/app/ClientLayout.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layouts_SideNav_SideBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layouts/SideNav/SideBar */ \"(app-pages-browser)/./src/components/Layouts/SideNav/SideBar.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/NotificationContext */ \"(app-pages-browser)/./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _components_Layouts_Header_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layouts/Header/page */ \"(app-pages-browser)/./src/components/Layouts/Header/page.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ClientLayout(param) {\n    let { children } = param;\n    _s();\n    const [sidebarExpanded, setSidebarExpanded] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const hideLayoutPaths = [\n        \"/\",\n        \"/courses\",\n        \"/courses/\",\n        \"/signin\",\n        \"/signin/\",\n        \"/signup\",\n        \"/signup/\",\n        \"/forgot-password\",\n        \"/forgot-password/\",\n        \"/my-dashboard\",\n        \"/my-dashboard/\",\n        \"/my-learning\",\n        \"/my-learning/\",\n        \"/my-certificates\",\n        \"/my-certificates/\"\n    ];\n    // Check for exact matches or dynamic course routes (including discussion pages)\n    const hideLayout = hideLayoutPaths.includes(pathname) || pathname.startsWith(\"/courses/\") && pathname.match(/^\\/courses\\/\\d+(\\/.*)?$/);\n    if (hideLayout) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n            lineNumber: 30,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_SideNav_SideBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sidebarExpanded: sidebarExpanded,\n                setSidebarExpanded: setSidebarExpanded\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layouts_Header_page__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            sidebarExpanded: sidebarExpanded\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientLayout, \"3pJkDrxumQImnX9YeMOlT01vQmU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname\n    ];\n});\n_c = ClientLayout;\nvar _c;\n$RefreshReg$(_c, \"ClientLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ClientLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2d78d9471ab0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHdpbmRvd3N2bTJcXERlc2t0b3BcXENobmFkcnVjb2RlXFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmQ3OGQ5NDcxYWIwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});