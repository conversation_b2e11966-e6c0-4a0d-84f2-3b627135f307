"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/courses/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CourseViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_DiscussionForum__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/DiscussionForum */ \"(app-pages-browser)/./src/components/DiscussionForum.tsx\");\n/* harmony import */ var _components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/QnAWithHeyGenModal */ \"(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\");\n/* harmony import */ var _components_AssessmentModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/AssessmentModal */ \"(app-pages-browser)/./src/components/AssessmentModal.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CourseViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const courseId = params.id;\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('Overview');\n    const [showAssessmentModal, setShowAssessmentModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [currentVideoIndex, setCurrentVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [leftSidebarVisible, setLeftSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [rightSidebarVisible, setRightSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [hasStartedCourse, setHasStartedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [courseProgress, setCourseProgress] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [moduleProgress, setModuleProgress] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [isQnAModalOpen, setIsQnAModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        return null;\n    };\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            if (user) {\n                setCurrentUser(user);\n            } else {\n                // Redirect to login if no valid token\n                window.location.href = '/signin';\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], []);\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch user progress\n    const fetchUserProgress = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[fetchUserProgress]\": async ()=>{\n            if (!userId) return; // Don't fetch if no user ID\n            try {\n                const progressResponse = await fetch(\"http://localhost:5001/api/users/\".concat(userId, \"/courses/\").concat(courseId, \"/progress\"));\n                if (progressResponse.ok) {\n                    const progressData = await progressResponse.json();\n                    setCourseProgress(progressData.courseProgress);\n                    setModuleProgress(progressData.moduleProgress);\n                    // Set current video index based on progress\n                    if (progressData.courseProgress && progressData.courseProgress.current_module > 1) {\n                        setCurrentVideoIndex(progressData.courseProgress.current_module - 1);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[fetchUserProgress]\"], [\n        userId,\n        courseId\n    ]);\n    // Function to start course (track course enrollment)\n    const startCourse = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[startCourse]\": async ()=>{\n            if (!userId) return; // Don't start if no user ID\n            try {\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/start\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId\n                    })\n                });\n                if (response.ok) {\n                    setHasStartedCourse(true);\n                    await fetchUserProgress(); // Refresh progress data\n                    console.log('Course started for course ID:', courseId);\n                }\n            } catch (error) {\n                console.error('Error starting course:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[startCourse]\"], [\n        userId,\n        courseId,\n        fetchUserProgress\n    ]);\n    // Function to update module progress\n    const updateModuleProgress = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[updateModuleProgress]\": async function(moduleNumber, watchTime, totalDuration) {\n            let isCompleted = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n            if (!userId) return; // Don't update if no user ID\n            try {\n                const currentVideo = videos[moduleNumber - 1];\n                const requestData = {\n                    userId,\n                    watchTime,\n                    totalDuration,\n                    isCompleted,\n                    moduleTitle: (currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.page_title) || \"Module \".concat(moduleNumber)\n                };\n                console.log('Updating module progress:', requestData);\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/modules/\").concat(moduleNumber, \"/progress\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(requestData)\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log('Progress update successful:', result);\n                    await fetchUserProgress(); // Refresh progress data\n                } else {\n                    const errorData = await response.json();\n                    console.error('Progress update failed:', errorData);\n                }\n            } catch (error) {\n                console.error('Error updating module progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[updateModuleProgress]\"], [\n        userId,\n        courseId,\n        videos,\n        fetchUserProgress\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const fetchCourseData = {\n                \"CourseViewPage.useEffect.fetchCourseData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch course details\n                        const courseResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (!courseResponse.ok) {\n                            throw new Error('Failed to fetch course details');\n                        }\n                        const courseData = await courseResponse.json();\n                        setCourse(courseData);\n                        // Fetch course videos\n                        const videosResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/videos\"));\n                        if (!videosResponse.ok) {\n                            throw new Error('Failed to fetch course videos');\n                        }\n                        const videosData = await videosResponse.json();\n                        setVideos(videosData);\n                        // Fetch user progress for this course (only if user is authenticated)\n                        if (userId) {\n                            await fetchUserProgress();\n                        }\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.fetchCourseData\"];\n            if (courseId && userId) {\n                fetchCourseData();\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        courseId,\n        userId\n    ]);\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"CourseViewPage.useEffect.handleKeyPress\": (event)=>{\n                    if (videos.length === 0) return;\n                    if (event.key === 'ArrowLeft' && currentVideoIndex > 0) {\n                        setCurrentVideoIndex(currentVideoIndex - 1);\n                    } else if (event.key === 'ArrowRight' && currentVideoIndex < videos.length - 1) {\n                        setCurrentVideoIndex(currentVideoIndex + 1);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"CourseViewPage.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"CourseViewPage.useEffect\"];\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        currentVideoIndex,\n        videos.length\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading course...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !course) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-red-600\",\n                            children: error || 'Course not found'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    const currentVideo = videos[currentVideoIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-64px)] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(leftSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-gray-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800\",\n                                                    children: \"Course Curriculum\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        videos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                videos.length,\n                                                \" modules\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto\",\n                                    children: videos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            videos.map((video, index)=>{\n                                                const moduleProgressData = moduleProgress.find((mp)=>mp.module_number === video.page_number);\n                                                const isCompleted = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.is_completed) || false;\n                                                const completionPercentage = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.completion_percentage) || 0;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(index === currentVideoIndex ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50', \" \").concat(isCompleted ? 'bg-green-50' : ''),\n                                                    onClick: ()=>{\n                                                        setCurrentVideoIndex(index);\n                                                        if (!hasStartedCourse) {\n                                                            startCourse();\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold \".concat(isCompleted ? 'bg-green-500 text-white' : index === currentVideoIndex ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'),\n                                                                children: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 31\n                                                                }, this) : index === currentVideoIndex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 31\n                                                                }, this) : video.page_number\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-sm leading-tight \".concat(isCompleted ? 'text-green-800' : index === currentVideoIndex ? 'text-blue-800' : 'text-gray-800'),\n                                                                        children: [\n                                                                            \"Module \",\n                                                                            video.page_number,\n                                                                            \": \",\n                                                                            video.page_title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs mt-1 \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                        children: [\n                                                                            video.avatar_name,\n                                                                            \" • 30 min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                                children: isCompleted ? 'Completed' : completionPercentage > 0 ? \"\".concat(Math.round(completionPercentage), \"% watched\") : 'Not started'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            completionPercentage > 0 && !isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 bg-gray-200 rounded-full h-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-blue-500 h-1 rounded-full transition-all duration-300\",\n                                                                                    style: {\n                                                                                        width: \"\".concat(completionPercentage, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, video.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 rounded-lg border cursor-pointer transition-all \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-50 border-green-200 hover:bg-green-100' : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'),\n                                                onClick: ()=>{\n                                                    if ((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100) {\n                                                        setShowAssessmentModal(true);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-100' : 'bg-gray-100'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-sm \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-800' : 'text-gray-500'),\n                                                                    children: \"Take Final Assessment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                    children: (courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'Click to start assessment' : 'Complete all modules to unlock assessment'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 text-center text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No curriculum available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setLeftSidebarVisible(!leftSidebarVisible),\n                            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                left: leftSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(leftSidebarVisible ? 'rotate-180' : ''),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black relative\",\n                                    children: [\n                                        videos.length > 0 && currentVideo && currentVideo.heygenbloburl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"w-full h-[400px] object-cover\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            onPlay: ()=>{\n                                                if (!hasStartedCourse) {\n                                                    startCourse();\n                                                }\n                                            },\n                                            onTimeUpdate: (e)=>{\n                                                const video = e.target;\n                                                const watchTime = Math.floor(video.currentTime);\n                                                const totalDuration = Math.floor(video.duration);\n                                                // Update progress every 10 seconds\n                                                if (watchTime % 10 === 0 && watchTime > 0) {\n                                                    updateModuleProgress(currentVideo.page_number, watchTime, totalDuration);\n                                                }\n                                            },\n                                            onEnded: ()=>{\n                                                const video = document.querySelector('video');\n                                                if (video) {\n                                                    const totalDuration = Math.floor(video.duration);\n                                                    updateModuleProgress(currentVideo.page_number, totalDuration, totalDuration, true);\n                                                }\n                                                // Auto-advance to next video after a short delay\n                                                setTimeout(()=>{\n                                                    if (currentVideoIndex < videos.length - 1) {\n                                                        setCurrentVideoIndex(currentVideoIndex + 1);\n                                                    }\n                                                }, 1500); // 1.5 second delay before auto-advancing\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                    src: currentVideo.heygenbloburl,\n                                                    type: \"video/mp4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Your browser does not support the video tag.\"\n                                            ]\n                                        }, currentVideo.heygenbloburl, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: videos.length === 0 ? 'No videos available for this course' : 'Loading video...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsQnAModalOpen(true),\n                                            className: \"absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10\",\n                                            title: \"Raise Hand\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 group-hover:animate-bounce\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 px-6\",\n                                        children: [\n                                            'Overview',\n                                            'Assessment',\n                                            'Discussion',\n                                            'Reviews'\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"py-4 font-medium border-b-2 transition-colors \".concat(activeTab === tab && tab !== 'Assessment' && tab !== 'Discussion' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                                onClick: ()=>{\n                                                    if (tab === 'Assessment') {\n                                                        setShowAssessmentModal(true);\n                                                    } else if (tab === 'Discussion') {\n                                                        router.push(\"/courses/\".concat(courseId, \"/discussion\"));\n                                                    } else {\n                                                        setActiveTab(tab);\n                                                    }\n                                                },\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 bg-white p-6 overflow-y-auto\",\n                                    children: [\n                                        activeTab === 'Overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"About This Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-6\",\n                                                    children: \"Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"What you'll learn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2 text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Industry best practices and standards\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Practical implementation strategies\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Real-world case studies and examples\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 542,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Professional certification preparation\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Prerequisites\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700 mb-4\",\n                                                                    children: \"Advanced knowledge and experience required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Course Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Domain: \",\n                                                                                course.domain\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Level: \",\n                                                                                course.level\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Duration: \",\n                                                                                course.estimated_hours,\n                                                                                \" hours\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Modules: \",\n                                                                                videos.length\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Certificate: Available upon completion\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        activeTab === 'Discussion' && (userId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiscussionForum__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            courseId: courseId,\n                                            userId: userId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Please log in to access the discussion forum.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, this)),\n                                        activeTab === 'Reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-8\",\n                                            children: \"Student reviews coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRightSidebarVisible(!rightSidebarVisible),\n                            className: \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                right: rightSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(rightSidebarVisible ? '' : 'rotate-180'),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(rightSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-2\",\n                                            children: \"Course Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-3\",\n                                            children: course.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium\",\n                                                children: course.domain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-yellow-400\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"4.8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"(234 reviews)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                course.estimated_hours,\n                                                                \" hours\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                videos.length,\n                                                                \" students\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: course.level\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Certificate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                videos.length > 0 && courseProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-3\",\n                                            children: \"Course Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Overall Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold text-orange-600\",\n                                                            children: [\n                                                                Math.round(courseProgress.progress_percentage),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2 mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(courseProgress.progress_percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                courseProgress.completed_modules,\n                                                                \" of \",\n                                                                courseProgress.total_modules,\n                                                                \" modules completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Status: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(courseProgress.status === 'completed' ? 'text-green-600' : courseProgress.status === 'in_progress' ? 'text-blue-600' : 'text-gray-600'),\n                                                                    children: courseProgress.status === 'completed' ? 'Completed' : courseProgress.status === 'in_progress' ? 'In Progress' : 'Not Started'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        courseProgress.started_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(courseProgress.started_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        courseProgress.completed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Completed: \",\n                                                                new Date(courseProgress.completed_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-blue-800 mb-1\",\n                                                    children: \"Currently Watching\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Module \",\n                                                        currentVideo.page_number,\n                                                        \": \",\n                                                        currentVideo.page_title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: currentVideo.avatar_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isQnAModalOpen,\n                onClose: ()=>setIsQnAModalOpen(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 721,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AssessmentModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAssessmentModal,\n                onClose: ()=>setShowAssessmentModal(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 728,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseViewPage, \"RVk41ZQYMnmK3mH1gQElnMAZbYc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = CourseViewPage;\nvar _c;\n$RefreshReg$(_c, \"CourseViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY291cnNlcy9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRWdEO0FBQ2tCO0FBQ007QUFDTjtBQUNGO0FBQ1Q7QUE0QnhDLFNBQVNVOztJQUN0QixNQUFNQyxTQUFTSCwwREFBU0E7SUFDeEIsTUFBTUksU0FBU0gsMERBQVNBO0lBQ3hCLE1BQU1JLFdBQVdGLE9BQU9HLEVBQUU7SUFFMUIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdYLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1kscUJBQXFCQyx1QkFBdUIsR0FBR2IsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDYyxRQUFRQyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFnQjtJQUNwRCxNQUFNLENBQUNnQixRQUFRQyxVQUFVLEdBQUdqQiwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUN0RCxNQUFNLENBQUNrQixtQkFBbUJDLHFCQUFxQixHQUFHbkIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDb0IsU0FBU0MsV0FBVyxHQUFHckIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDc0IsT0FBT0MsU0FBUyxHQUFHdkIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3dCLG9CQUFvQkMsc0JBQXNCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUMwQixxQkFBcUJDLHVCQUF1QixHQUFHM0IsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDNEIsa0JBQWtCQyxvQkFBb0IsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhCLGdCQUFnQkMsa0JBQWtCLEdBQUcvQiwrQ0FBUUEsQ0FBTTtJQUMxRCxNQUFNLENBQUNnQyxnQkFBZ0JDLGtCQUFrQixHQUFHakMsK0NBQVFBLENBQVEsRUFBRTtJQUM5RCxNQUFNLENBQUNrQyxhQUFhQyxlQUFlLEdBQUduQywrQ0FBUUEsQ0FBTTtJQUNwRCxNQUFNLENBQUNvQyxnQkFBZ0JDLGtCQUFrQixHQUFHckMsK0NBQVFBLENBQUM7SUFHckQsNkNBQTZDO0lBQzdDLE1BQU1zQyxtQkFBbUI7UUFDdkIsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1FBQ25DLElBQUlGLE9BQU87WUFDVCxJQUFJO2dCQUNGLE1BQU1HLFlBQVlILE1BQU1JLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDckMsTUFBTUMsU0FBU0YsVUFBVUcsT0FBTyxDQUFDLE1BQU0sS0FBS0EsT0FBTyxDQUFDLE1BQU07Z0JBQzFELE1BQU1DLGNBQWNDLG1CQUFtQkMsS0FBS0osUUFBUUQsS0FBSyxDQUFDLElBQUlNLEdBQUcsQ0FBQyxTQUFTQyxDQUFDO29CQUMxRSxPQUFPLE1BQU0sQ0FBQyxPQUFPQSxFQUFFQyxVQUFVLENBQUMsR0FBR0MsUUFBUSxDQUFDLEdBQUUsRUFBR0MsS0FBSyxDQUFDLENBQUM7Z0JBQzVELEdBQUdDLElBQUksQ0FBQztnQkFDUixPQUFPQyxLQUFLQyxLQUFLLENBQUNWO1lBQ3BCLEVBQUUsT0FBT3hCLE9BQU87Z0JBQ2RtQyxRQUFRbkMsS0FBSyxDQUFDLHlCQUF5QkE7Z0JBQ3ZDLE9BQU87WUFDVDtRQUNGO1FBQ0EsT0FBTztJQUNUO0lBRUEsNkJBQTZCO0lBQzdCckIsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXlELE9BQU9wQjtZQUNiLElBQUlvQixNQUFNO2dCQUNSdkIsZUFBZXVCO1lBQ2pCLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Q0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7WUFDekI7UUFDRjttQ0FBRyxFQUFFO0lBRUwsTUFBTUMsU0FBUzVCLHdCQUFBQSxrQ0FBQUEsWUFBYXpCLEVBQUU7SUFFOUIsc0JBQXNCO0lBQ3RCLE1BQU1zRCxvQkFBb0I3RCxrREFBV0E7eURBQUM7WUFDcEMsSUFBSSxDQUFDNEQsUUFBUSxRQUFRLDRCQUE0QjtZQUVqRCxJQUFJO2dCQUNGLE1BQU1FLG1CQUFtQixNQUFNQyxNQUFNLG1DQUFxRHpELE9BQWxCc0QsUUFBTyxhQUFvQixPQUFUdEQsVUFBUztnQkFDbkcsSUFBSXdELGlCQUFpQkUsRUFBRSxFQUFFO29CQUN2QixNQUFNQyxlQUFlLE1BQU1ILGlCQUFpQkksSUFBSTtvQkFDaERyQyxrQkFBa0JvQyxhQUFhckMsY0FBYztvQkFDN0NHLGtCQUFrQmtDLGFBQWFuQyxjQUFjO29CQUU3Qyw0Q0FBNEM7b0JBQzVDLElBQUltQyxhQUFhckMsY0FBYyxJQUFJcUMsYUFBYXJDLGNBQWMsQ0FBQ3VDLGNBQWMsR0FBRyxHQUFHO3dCQUNqRmxELHFCQUFxQmdELGFBQWFyQyxjQUFjLENBQUN1QyxjQUFjLEdBQUc7b0JBQ3BFO2dCQUNGO1lBQ0YsRUFBRSxPQUFPL0MsT0FBTztnQkFDZG1DLFFBQVFuQyxLQUFLLENBQUMsNEJBQTRCQTtZQUM1QztRQUNGO3dEQUFHO1FBQUN3QztRQUFRdEQ7S0FBUztJQUVyQixxREFBcUQ7SUFDckQsTUFBTThELGNBQWNwRSxrREFBV0E7bURBQUM7WUFDOUIsSUFBSSxDQUFDNEQsUUFBUSxRQUFRLDRCQUE0QjtZQUVqRCxJQUFJO2dCQUNGLE1BQU1TLFdBQVcsTUFBTU4sTUFBTSxxQ0FBOEMsT0FBVHpELFVBQVMsV0FBUztvQkFDbEZnRSxRQUFRO29CQUNSQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7b0JBQ0FDLE1BQU1uQixLQUFLb0IsU0FBUyxDQUFDO3dCQUFFYjtvQkFBTztnQkFDaEM7Z0JBRUEsSUFBSVMsU0FBU0wsRUFBRSxFQUFFO29CQUNmckMsb0JBQW9CO29CQUNwQixNQUFNa0MscUJBQXFCLHdCQUF3QjtvQkFDbkROLFFBQVFtQixHQUFHLENBQUMsaUNBQWlDcEU7Z0JBQy9DO1lBQ0YsRUFBRSxPQUFPYyxPQUFPO2dCQUNkbUMsUUFBUW5DLEtBQUssQ0FBQywwQkFBMEJBO1lBQzFDO1FBQ0Y7a0RBQUc7UUFBQ3dDO1FBQVF0RDtRQUFVdUQ7S0FBa0I7SUFFeEMscUNBQXFDO0lBQ3JDLE1BQU1jLHVCQUF1QjNFLGtEQUFXQTs0REFBQyxlQUFPNEUsY0FBc0JDLFdBQW1CQztnQkFBdUJDLCtFQUF1QjtZQUNySSxJQUFJLENBQUNuQixRQUFRLFFBQVEsNkJBQTZCO1lBRWxELElBQUk7Z0JBQ0YsTUFBTW9CLGVBQWVsRSxNQUFNLENBQUM4RCxlQUFlLEVBQUU7Z0JBQzdDLE1BQU1LLGNBQWM7b0JBQ2xCckI7b0JBQ0FpQjtvQkFDQUM7b0JBQ0FDO29CQUNBRyxhQUFhRixDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNHLFVBQVUsS0FBSSxVQUF1QixPQUFiUDtnQkFDckQ7Z0JBRUFyQixRQUFRbUIsR0FBRyxDQUFDLDZCQUE2Qk87Z0JBRXpDLE1BQU1aLFdBQVcsTUFBTU4sTUFBTSxxQ0FBeURhLE9BQXBCdEUsVUFBUyxhQUF3QixPQUFic0UsY0FBYSxjQUFZO29CQUM3R04sUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNbkIsS0FBS29CLFNBQVMsQ0FBQ1E7Z0JBQ3ZCO2dCQUVBLElBQUlaLFNBQVNMLEVBQUUsRUFBRTtvQkFDZixNQUFNb0IsU0FBUyxNQUFNZixTQUFTSCxJQUFJO29CQUNsQ1gsUUFBUW1CLEdBQUcsQ0FBQywrQkFBK0JVO29CQUMzQyxNQUFNdkIscUJBQXFCLHdCQUF3QjtnQkFDckQsT0FBTztvQkFDTCxNQUFNd0IsWUFBWSxNQUFNaEIsU0FBU0gsSUFBSTtvQkFDckNYLFFBQVFuQyxLQUFLLENBQUMsMkJBQTJCaUU7Z0JBQzNDO1lBQ0YsRUFBRSxPQUFPakUsT0FBTztnQkFDZG1DLFFBQVFuQyxLQUFLLENBQUMsbUNBQW1DQTtZQUNuRDtRQUNGOzJEQUFHO1FBQUN3QztRQUFRdEQ7UUFBVVE7UUFBUStDO0tBQWtCO0lBSWhEOUQsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXVGOzREQUFrQjtvQkFDdEIsSUFBSTt3QkFDRm5FLFdBQVc7d0JBRVgsdUJBQXVCO3dCQUN2QixNQUFNb0UsaUJBQWlCLE1BQU14QixNQUFNLHFDQUE4QyxPQUFUekQ7d0JBQ3hFLElBQUksQ0FBQ2lGLGVBQWV2QixFQUFFLEVBQUU7NEJBQ3RCLE1BQU0sSUFBSXdCLE1BQU07d0JBQ2xCO3dCQUNBLE1BQU1DLGFBQWEsTUFBTUYsZUFBZXJCLElBQUk7d0JBQzVDckQsVUFBVTRFO3dCQUVWLHNCQUFzQjt3QkFDdEIsTUFBTUMsaUJBQWlCLE1BQU0zQixNQUFNLHFDQUE4QyxPQUFUekQsVUFBUzt3QkFDakYsSUFBSSxDQUFDb0YsZUFBZTFCLEVBQUUsRUFBRTs0QkFDdEIsTUFBTSxJQUFJd0IsTUFBTTt3QkFDbEI7d0JBQ0EsTUFBTUcsYUFBYSxNQUFNRCxlQUFleEIsSUFBSTt3QkFDNUNuRCxVQUFVNEU7d0JBRVYsc0VBQXNFO3dCQUN0RSxJQUFJL0IsUUFBUTs0QkFDVixNQUFNQzt3QkFDUjtvQkFFRixFQUFFLE9BQU8rQixLQUFjO3dCQUNyQnZFLFNBQVN1RSxlQUFlSixRQUFRSSxJQUFJQyxPQUFPLEdBQUc7b0JBQ2hELFNBQVU7d0JBQ1IxRSxXQUFXO29CQUNiO2dCQUNGOztZQUVBLElBQUliLFlBQVlzRCxRQUFRO2dCQUN0QjBCO1lBQ0Y7UUFDRjttQ0FBRztRQUFDaEY7UUFBVXNEO0tBQU87SUFFckIsc0JBQXNCO0lBQ3RCN0QsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTStGOzJEQUFpQixDQUFDQztvQkFDdEIsSUFBSWpGLE9BQU9rRixNQUFNLEtBQUssR0FBRztvQkFFekIsSUFBSUQsTUFBTUUsR0FBRyxLQUFLLGVBQWVqRixvQkFBb0IsR0FBRzt3QkFDdERDLHFCQUFxQkQsb0JBQW9CO29CQUMzQyxPQUFPLElBQUkrRSxNQUFNRSxHQUFHLEtBQUssZ0JBQWdCakYsb0JBQW9CRixPQUFPa0YsTUFBTSxHQUFHLEdBQUc7d0JBQzlFL0UscUJBQXFCRCxvQkFBb0I7b0JBQzNDO2dCQUNGOztZQUVBeUMsT0FBT3lDLGdCQUFnQixDQUFDLFdBQVdKO1lBQ25DOzRDQUFPLElBQU1yQyxPQUFPMEMsbUJBQW1CLENBQUMsV0FBV0w7O1FBQ3JEO21DQUFHO1FBQUM5RTtRQUFtQkYsT0FBT2tGLE1BQU07S0FBQztJQUVyQyxJQUFJOUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDa0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUM1RywwREFBTUE7Ozs7OzhCQUNQLDhEQUFDNkc7b0JBQUtELFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLakQ7SUFFQSxJQUFJakYsU0FBUyxDQUFDUixRQUFRO1FBQ3BCLHFCQUNFLDhEQUFDd0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUM1RywwREFBTUE7Ozs7OzhCQUNQLDhEQUFDNkc7b0JBQUtELFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWmpGLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdEI7SUFFQSxNQUFNNEQsZUFBZWxFLE1BQU0sQ0FBQ0Usa0JBQWtCO0lBRTlDLHFCQUNFLDhEQUFDb0Y7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUM1RywwREFBTUE7Ozs7OzBCQUNQLDhEQUFDNkc7Z0JBQUtELFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVcsR0FBdUMsT0FBcEMvRSxxQkFBcUIsU0FBUyxPQUFNOzs4Q0FDckQsOERBQUM4RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUlGLFdBQVU7b0RBQXdCRyxNQUFLO29EQUFlQyxTQUFROzhEQUNqRSw0RUFBQ0M7d0RBQUtDLFVBQVM7d0RBQVVDLEdBQUU7d0RBQW1RQyxVQUFTOzs7Ozs7Ozs7Ozs4REFFelMsOERBQUNDO29EQUFHVCxXQUFVOzhEQUFzQzs7Ozs7Ozs7Ozs7O3dDQUVyRHZGLE9BQU9rRixNQUFNLEdBQUcsbUJBQ2YsOERBQUNlOzRDQUFFVixXQUFVOztnREFBeUJ2RixPQUFPa0YsTUFBTTtnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FJeEQsOERBQUNJO29DQUFJQyxXQUFVOzhDQUNadkYsT0FBT2tGLE1BQU0sR0FBRyxrQkFDZiw4REFBQ0k7d0NBQUlDLFdBQVU7OzRDQUNadkYsT0FBT2lDLEdBQUcsQ0FBQyxDQUFDaUUsT0FBT0M7Z0RBQ2xCLE1BQU1DLHFCQUFxQnBGLGVBQWVxRixJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdDLGFBQWEsS0FBS0wsTUFBTU0sV0FBVztnREFDM0YsTUFBTXZDLGNBQWNtQyxDQUFBQSwrQkFBQUEseUNBQUFBLG1CQUFvQkssWUFBWSxLQUFJO2dEQUN4RCxNQUFNQyx1QkFBdUJOLENBQUFBLCtCQUFBQSx5Q0FBQUEsbUJBQW9CTyxxQkFBcUIsS0FBSTtnREFFMUUscUJBQ0UsOERBQUNyQjtvREFFQ0MsV0FBVyxrRUFJUHRCLE9BSEZrQyxVQUFVakcsb0JBQ04sMENBQ0Esb0JBQ0wsS0FBb0MsT0FBakMrRCxjQUFjLGdCQUFnQjtvREFDbEMyQyxTQUFTO3dEQUNQekcscUJBQXFCZ0c7d0RBQ3JCLElBQUksQ0FBQ3ZGLGtCQUFrQjs0REFDckIwQzt3REFDRjtvREFDRjs4REFFQSw0RUFBQ2dDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVcsNkZBTWYsT0FMQ3RCLGNBQ0ksNEJBQ0FrQyxVQUFVakcsb0JBQ1IsMkJBQ0E7MEVBRUwrRCw0QkFDQyw4REFBQ3dCO29FQUFJRixXQUFVO29FQUFVRyxNQUFLO29FQUFlQyxTQUFROzhFQUNuRCw0RUFBQ0M7d0VBQUtDLFVBQVM7d0VBQVVDLEdBQUU7d0VBQXFIQyxVQUFTOzs7Ozs7Ozs7OzJFQUV6SkksVUFBVWpHLGtDQUNaLDhEQUFDdUY7b0VBQUlGLFdBQVU7b0VBQVVHLE1BQUs7b0VBQWVDLFNBQVE7OEVBQ25ELDRFQUFDQzt3RUFBS0MsVUFBUzt3RUFBVUMsR0FBRTt3RUFBMEdDLFVBQVM7Ozs7Ozs7Ozs7MkVBR2hKRyxNQUFNTSxXQUFXOzs7Ozs7MEVBR3JCLDhEQUFDbEI7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDc0I7d0VBQUd0QixXQUFXLHFDQUdkLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7OzRFQUM5Qzs0RUFDTWdHLE1BQU1NLFdBQVc7NEVBQUM7NEVBQUdOLE1BQU03QixVQUFVOzs7Ozs7O2tGQUUvQyw4REFBQzRCO3dFQUFFVixXQUFXLGdCQUdiLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7OzRFQUUvQ2dHLE1BQU1ZLFdBQVc7NEVBQUM7Ozs7Ozs7a0ZBRXJCLDhEQUFDeEI7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDVTtnRkFBRVYsV0FBVyxXQUdiLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7MEZBRS9DK0QsY0FBYyxjQUFjeUMsdUJBQXVCLElBQUksR0FBb0MsT0FBakNLLEtBQUtDLEtBQUssQ0FBQ04sdUJBQXNCLGVBQWE7Ozs7Ozs0RUFFMUdBLHVCQUF1QixLQUFLLENBQUN6Qyw2QkFDNUIsOERBQUNxQjtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ0Q7b0ZBQ0NDLFdBQVU7b0ZBQ1YwQixPQUFPO3dGQUFFQyxPQUFPLEdBQXdCLE9BQXJCUixzQkFBcUI7b0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQXpEbERSLE1BQU16RyxFQUFFOzs7Ozs0Q0FrRW5COzBEQUdBLDhEQUFDNkY7Z0RBQ0NDLFdBQVcsNERBSVYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxvREFDQTtnREFFTlAsU0FBUztvREFDUCxJQUFJOUYsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLEtBQUs7d0RBQy9DdEgsdUJBQXVCO29EQUN6QjtnREFDRjswREFFQSw0RUFBQ3lGO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVcseURBSWYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxpQkFDQTtzRUFFSiw0RUFBQzFCO2dFQUFJRixXQUFXLFdBSWYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxtQkFDQTtnRUFDRnpCLE1BQUs7Z0VBQWVDLFNBQVE7MEVBQzlCLDRFQUFDQztvRUFBS0MsVUFBUztvRUFBVUMsR0FBRTtvRUFBcUhDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBRzdKLDhEQUFDVDs7OEVBQ0MsOERBQUN1QjtvRUFBR3RCLFdBQVcsdUJBSWQsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxtQkFDQTs4RUFDRjs7Ozs7OzhFQUNKLDhEQUFDbEI7b0VBQUVWLFdBQVcsV0FJYixPQUhDekUsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLE1BQ3BDLG1CQUNBOzhFQUVIckcsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLE1BQ3JDLDhCQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQU9kLDhEQUFDN0I7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNVO3NEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9YLDhEQUFDbUI7NEJBQ0NSLFNBQVMsSUFBTW5HLHNCQUFzQixDQUFDRDs0QkFDdEMrRSxXQUFVOzRCQUNWMEIsT0FBTztnQ0FBRUksTUFBTTdHLHFCQUFxQixVQUFVOzRCQUFNO3NDQUVwRCw0RUFBQ2lGO2dDQUNDRixXQUFXLDZDQUFvRixPQUF2Qy9FLHFCQUFxQixlQUFlO2dDQUM1RmtGLE1BQUs7Z0NBQ0xDLFNBQVE7MENBRVIsNEVBQUNDO29DQUFLQyxVQUFTO29DQUFVQyxHQUFFO29DQUFxSEMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLN0osOERBQUNUOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3dDQUNadkYsT0FBT2tGLE1BQU0sR0FBRyxLQUFLaEIsZ0JBQWdCQSxhQUFhb0QsYUFBYSxpQkFDOUQsOERBQUNwQjs0Q0FFQ1gsV0FBVTs0Q0FDVmdDLFFBQVE7NENBQ1JDLFFBQVE7NENBQ1JDLFFBQVE7Z0RBQ04sSUFBSSxDQUFDN0csa0JBQWtCO29EQUNyQjBDO2dEQUNGOzRDQUNGOzRDQUNBb0UsY0FBYyxDQUFDQztnREFDYixNQUFNekIsUUFBUXlCLEVBQUVDLE1BQU07Z0RBQ3RCLE1BQU03RCxZQUFZZ0QsS0FBS2MsS0FBSyxDQUFDM0IsTUFBTTRCLFdBQVc7Z0RBQzlDLE1BQU05RCxnQkFBZ0IrQyxLQUFLYyxLQUFLLENBQUMzQixNQUFNNkIsUUFBUTtnREFFL0MsbUNBQW1DO2dEQUNuQyxJQUFJaEUsWUFBWSxPQUFPLEtBQUtBLFlBQVksR0FBRztvREFDekNGLHFCQUFxQkssYUFBYXNDLFdBQVcsRUFBRXpDLFdBQVdDO2dEQUM1RDs0Q0FDRjs0Q0FDQWdFLFNBQVM7Z0RBQ1AsTUFBTTlCLFFBQVErQixTQUFTQyxhQUFhLENBQUM7Z0RBQ3JDLElBQUloQyxPQUFPO29EQUNULE1BQU1sQyxnQkFBZ0IrQyxLQUFLYyxLQUFLLENBQUMzQixNQUFNNkIsUUFBUTtvREFDL0NsRSxxQkFBcUJLLGFBQWFzQyxXQUFXLEVBQUV4QyxlQUFlQSxlQUFlO2dEQUMvRTtnREFFQSxpREFBaUQ7Z0RBQ2pEbUUsV0FBVztvREFDVCxJQUFJakksb0JBQW9CRixPQUFPa0YsTUFBTSxHQUFHLEdBQUc7d0RBQ3pDL0UscUJBQXFCRCxvQkFBb0I7b0RBQzNDO2dEQUNGLEdBQUcsT0FBTyx5Q0FBeUM7NENBQ3JEOzs4REFFQSw4REFBQ2tJO29EQUFPQyxLQUFLbkUsYUFBYW9ELGFBQWE7b0RBQUVnQixNQUFLOzs7Ozs7Z0RBQWM7OzJDQWxDdkRwRSxhQUFhb0QsYUFBYTs7OztpRUFzQ2pDLDhEQUFDaEM7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNnRDtnREFBS2hELFdBQVU7MERBQ2J2RixPQUFPa0YsTUFBTSxLQUFLLElBQUksd0NBQXdDOzs7Ozs7Ozs7OztzREFNckUsOERBQUNrQzs0Q0FDQ1IsU0FBUyxJQUFNdkYsa0JBQWtCOzRDQUNqQ2tFLFdBQVU7NENBQ1ZpRCxPQUFNO3NEQUVOLDRFQUFDL0M7Z0RBQ0NGLFdBQVU7Z0RBQ1ZHLE1BQUs7Z0RBQ0xDLFNBQVE7MERBRVIsNEVBQUNDO29EQUFLQyxVQUFTO29EQUFVQyxHQUFFO29EQUFrS0MsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNNU0sOERBQUNUO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjs0Q0FBQzs0Q0FBWTs0Q0FBYzs0Q0FBYzt5Q0FBVSxDQUFDdEQsR0FBRyxDQUFDLENBQUN3RyxvQkFDeEQsOERBQUNyQjtnREFFQzdCLFdBQVcsaURBSVYsT0FIQzdGLGNBQWMrSSxPQUFPQSxRQUFRLGdCQUFnQkEsUUFBUSxlQUNqRCxzQ0FDQTtnREFFTjdCLFNBQVM7b0RBQ1AsSUFBSTZCLFFBQVEsY0FBYzt3REFDeEI1SSx1QkFBdUI7b0RBQ3pCLE9BQU8sSUFBSTRJLFFBQVEsY0FBYzt3REFDL0JsSixPQUFPbUosSUFBSSxDQUFDLFlBQXFCLE9BQVRsSixVQUFTO29EQUNuQyxPQUFPO3dEQUNMRyxhQUFhOEk7b0RBQ2Y7Z0RBQ0Y7MERBRUNBOytDQWhCSUE7Ozs7Ozs7Ozs7Ozs7Ozs4Q0F1QmIsOERBQUNuRDtvQ0FBSUMsV0FBVTs7d0NBQ1o3RixjQUFjLDRCQUNiOzs4REFDRSw4REFBQ3NHO29EQUFHVCxXQUFVOzhEQUEwQjs7Ozs7OzhEQUN4Qyw4REFBQ1U7b0RBQUVWLFdBQVU7OERBQXFCOzs7Ozs7OERBSWxDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQ3VCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNvRDtvRUFBR3BELFdBQVU7O3NGQUNaLDhEQUFDcUQ7NEVBQUdyRCxXQUFVOzs4RkFDWiw4REFBQ2dEO29GQUFLaEQsV0FBVTs4RkFBc0I7Ozs7OztnRkFBUTs7Ozs7OztzRkFHaEQsOERBQUNxRDs0RUFBR3JELFdBQVU7OzhGQUNaLDhEQUFDZ0Q7b0ZBQUtoRCxXQUFVOzhGQUFzQjs7Ozs7O2dGQUFROzs7Ozs7O3NGQUdoRCw4REFBQ3FEOzRFQUFHckQsV0FBVTs7OEZBQ1osOERBQUNnRDtvRkFBS2hELFdBQVU7OEZBQXNCOzs7Ozs7Z0ZBQVE7Ozs7Ozs7c0ZBR2hELDhEQUFDcUQ7NEVBQUdyRCxXQUFVOzs4RkFDWiw4REFBQ2dEO29GQUFLaEQsV0FBVTs4RkFBc0I7Ozs7OztnRkFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFNcEQsOERBQUNEOzs4RUFDQyw4REFBQ3VCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNVO29FQUFFVixXQUFVOzhFQUFxQjs7Ozs7OzhFQUlsQyw4REFBQ3NCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNvRDtvRUFBR3BELFdBQVU7O3NGQUNaLDhEQUFDcUQ7O2dGQUFHO2dGQUFTOUksT0FBTytJLE1BQU07Ozs7Ozs7c0ZBQzFCLDhEQUFDRDs7Z0ZBQUc7Z0ZBQVE5SSxPQUFPZ0osS0FBSzs7Ozs7OztzRkFDeEIsOERBQUNGOztnRkFBRztnRkFBVzlJLE9BQU9pSixlQUFlO2dGQUFDOzs7Ozs7O3NGQUN0Qyw4REFBQ0g7O2dGQUFHO2dGQUFVNUksT0FBT2tGLE1BQU07Ozs7Ozs7c0ZBQzNCLDhEQUFDMEQ7c0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQVNibEosY0FBYyxnQkFDYm9ELENBQUFBLHVCQUNFLDhEQUFDbEUsbUVBQWVBOzRDQUFDWSxVQUFVQTs0Q0FBVXNELFFBQVFBOzs7OztpRUFFN0MsOERBQUN3Qzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ1U7Z0RBQUVWLFdBQVU7MERBQWdCOzs7Ozs7Ozs7O2dEQUVqQzt3Q0FHRDdGLGNBQWMsMkJBQ2IsOERBQUM0Rjs0Q0FBSUMsV0FBVTtzREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNdEQsOERBQUM2Qjs0QkFDQ1IsU0FBUyxJQUFNakcsdUJBQXVCLENBQUNEOzRCQUN2QzZFLFdBQVU7NEJBQ1YwQixPQUFPO2dDQUFFK0IsT0FBT3RJLHNCQUFzQixVQUFVOzRCQUFNO3NDQUV0RCw0RUFBQytFO2dDQUNDRixXQUFXLDZDQUFxRixPQUF4QzdFLHNCQUFzQixLQUFLO2dDQUNuRmdGLE1BQUs7Z0NBQ0xDLFNBQVE7MENBRVIsNEVBQUNDO29DQUFLQyxVQUFTO29DQUFVQyxHQUFFO29DQUFxSEMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLN0osOERBQUNUOzRCQUFJQyxXQUFXLEdBQXdDLE9BQXJDN0Usc0JBQXNCLFNBQVMsT0FBTTs7OENBQ3RELDhEQUFDNEU7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDc0I7NENBQUd0QixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ1M7NENBQUdULFdBQVU7c0RBQXdDekYsT0FBTzBJLEtBQUs7Ozs7OztzREFFbEUsOERBQUNsRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2dEO2dEQUFLaEQsV0FBVTswREFDYnpGLE9BQU8rSSxNQUFNOzs7Ozs7Ozs7OztzREFJbEIsOERBQUN2RDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRTt3REFBSUYsV0FBVTt3REFBMEJHLE1BQUs7d0RBQWVDLFNBQVE7a0VBQ25FLDRFQUFDQzs0REFBS0UsR0FBRTs7Ozs7Ozs7Ozs7a0VBRVYsOERBQUN5Qzt3REFBS2hELFdBQVU7a0VBQWM7Ozs7OztrRUFDOUIsOERBQUNnRDtrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSVYsOERBQUNqRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUlGLFdBQVU7NERBQVVHLE1BQUs7NERBQWVDLFNBQVE7c0VBQ25ELDRFQUFDQztnRUFBS0MsVUFBUztnRUFBVUMsR0FBRTtnRUFBcUhDLFVBQVM7Ozs7Ozs7Ozs7O3NFQUUzSiw4REFBQ3dDOztnRUFBTXpJLE9BQU9pSixlQUFlO2dFQUFDOzs7Ozs7Ozs7Ozs7OzhEQUVoQyw4REFBQ3pEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUlGLFdBQVU7NERBQVVHLE1BQUs7NERBQWVDLFNBQVE7c0VBQ25ELDRFQUFDQztnRUFBS0UsR0FBRTs7Ozs7Ozs7Ozs7c0VBRVYsOERBQUN5Qzs7Z0VBQU12SSxPQUFPa0YsTUFBTTtnRUFBQzs7Ozs7Ozs7Ozs7Ozs4REFFdkIsOERBQUNJO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUlGLFdBQVU7NERBQVVHLE1BQUs7NERBQWVDLFNBQVE7c0VBQ25ELDRFQUFDQztnRUFBS0MsVUFBUztnRUFBVUMsR0FBRTtnRUFBbVFDLFVBQVM7Ozs7Ozs7Ozs7O3NFQUV6Uyw4REFBQ3dDO3NFQUFNekksT0FBT2dKLEtBQUs7Ozs7Ozs7Ozs7Ozs4REFFckIsOERBQUN4RDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNFOzREQUFJRixXQUFVOzREQUFVRyxNQUFLOzREQUFlQyxTQUFRO3NFQUNuRCw0RUFBQ0M7Z0VBQUtDLFVBQVM7Z0VBQVVDLEdBQUU7Z0VBQWtpQkMsVUFBUzs7Ozs7Ozs7Ozs7c0VBRXhrQiw4REFBQ3dDO3NFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBdUJYdkksT0FBT2tGLE1BQU0sR0FBRyxLQUFLcEUsZ0NBQ3BCLDhEQUFDd0U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDMEQ7NENBQUcxRCxXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNnRDs0REFBS2hELFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ3BELDhEQUFDZ0Q7NERBQUtoRCxXQUFVOztnRUFDYndCLEtBQUtDLEtBQUssQ0FBQ2xHLGVBQWVxRyxtQkFBbUI7Z0VBQUU7Ozs7Ozs7Ozs7Ozs7OERBR3BELDhEQUFDN0I7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUNDQyxXQUFVO3dEQUNWMEIsT0FBTzs0REFBRUMsT0FBTyxHQUFzQyxPQUFuQ3BHLGVBQWVxRyxtQkFBbUIsRUFBQzt3REFBRzs7Ozs7Ozs7Ozs7OERBRzdELDhEQUFDN0I7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDVTs7Z0VBQUduRixlQUFlb0ksaUJBQWlCO2dFQUFDO2dFQUFLcEksZUFBZXFJLGFBQWE7Z0VBQUM7Ozs7Ozs7c0VBQ3ZFLDhEQUFDbEQ7O2dFQUFFOzhFQUFRLDhEQUFDc0M7b0VBQUtoRCxXQUFXLGVBRzNCLE9BRkN6RSxlQUFlc0ksTUFBTSxLQUFLLGNBQWMsbUJBQ3hDdEksZUFBZXNJLE1BQU0sS0FBSyxnQkFBZ0Isa0JBQWtCOzhFQUUzRHRJLGVBQWVzSSxNQUFNLEtBQUssY0FBYyxjQUN4Q3RJLGVBQWVzSSxNQUFNLEtBQUssZ0JBQWdCLGdCQUFnQjs7Ozs7Ozs7Ozs7O3dEQUU1RHRJLGVBQWV1SSxVQUFVLGtCQUN4Qiw4REFBQ3BEOztnRUFBRTtnRUFBVSxJQUFJcUQsS0FBS3hJLGVBQWV1SSxVQUFVLEVBQUVFLGtCQUFrQjs7Ozs7Ozt3REFFcEV6SSxlQUFlMEksWUFBWSxrQkFDMUIsOERBQUN2RDs7Z0VBQUU7Z0VBQVksSUFBSXFELEtBQUt4SSxlQUFlMEksWUFBWSxFQUFFRCxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBTTVFckYsOEJBQ0MsOERBQUNvQjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNrRTtvREFBR2xFLFdBQVU7OERBQXlDOzs7Ozs7OERBQ3ZELDhEQUFDVTtvREFBRVYsV0FBVTs7d0RBQXdCO3dEQUMzQnJCLGFBQWFzQyxXQUFXO3dEQUFDO3dEQUFHdEMsYUFBYUcsVUFBVTs7Ozs7Ozs4REFFN0QsOERBQUM0QjtvREFBRVYsV0FBVTs4REFDVnJCLGFBQWE0QyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXekMsOERBQUNqSSxzRUFBa0JBO2dCQUNqQjZLLFFBQVF0STtnQkFDUnVJLFNBQVMsSUFBTXRJLGtCQUFrQjtnQkFDakM3QixVQUFVQTs7Ozs7OzBCQUlaLDhEQUFDVixtRUFBZUE7Z0JBQ2Q0SyxRQUFROUo7Z0JBQ1IrSixTQUFTLElBQU05Six1QkFBdUI7Z0JBQ3RDTCxVQUFVQTs7Ozs7Ozs7Ozs7O0FBSWxCO0dBM3JCd0JIOztRQUNQRixzREFBU0E7UUFDVEMsc0RBQVNBOzs7S0FGRkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcd2luZG93c3ZtMlxcRGVza3RvcFxcQ2huYWRydWNvZGVcXGZyb250ZW5kXFxzcmNcXGFwcFxcY291cnNlc1xcW2lkXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IE5hdmJhciBmcm9tICcuLi8uLi8uLi9jb21wb25lbnRzL05hdmJhcic7XHJcbmltcG9ydCBEaXNjdXNzaW9uRm9ydW0gZnJvbSAnLi4vLi4vLi4vY29tcG9uZW50cy9EaXNjdXNzaW9uRm9ydW0nO1xyXG5pbXBvcnQgUW5BV2l0aEhleUdlbk1vZGFsIGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvUW5BV2l0aEhleUdlbk1vZGFsJztcclxuaW1wb3J0IEFzc2Vzc21lbnRNb2RhbCBmcm9tICcuLi8uLi8uLi9jb21wb25lbnRzL0Fzc2Vzc21lbnRNb2RhbCc7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5cclxuaW50ZXJmYWNlIENvdXJzZSB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgZG9tYWluOiBzdHJpbmc7XHJcbiAgbGV2ZWw6IHN0cmluZztcclxuICBlc3RpbWF0ZWRfaG91cnM6IG51bWJlcjtcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBDb3Vyc2VWaWRlbyB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICBwYWdlX251bWJlcjogbnVtYmVyO1xyXG4gIHBhZ2VfdGl0bGU6IHN0cmluZztcclxuICB2aWRlb19pZDogc3RyaW5nO1xyXG4gIGdlbmVyYXRpb25fc3RhdHVzOiBzdHJpbmc7XHJcbiAgdmlkZW9zdGF0dXNhcGk6IHN0cmluZztcclxuICBoZXlnZW52aWRlb3VybDogc3RyaW5nO1xyXG4gIGhleWdlbmJsb2J1cmw6IHN0cmluZztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgYXZhdGFyX25hbWU6IHN0cmluZztcclxuICB2ZXJzaW9uX251bWJlcjogbnVtYmVyO1xyXG59XHJcblxyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvdXJzZVZpZXdQYWdlKCkge1xyXG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IGNvdXJzZUlkID0gcGFyYW1zLmlkIGFzIHN0cmluZztcclxuXHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdPdmVydmlldycpO1xyXG4gIGNvbnN0IFtzaG93QXNzZXNzbWVudE1vZGFsLCBzZXRTaG93QXNzZXNzbWVudE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY291cnNlLCBzZXRDb3Vyc2VdID0gdXNlU3RhdGU8Q291cnNlIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3ZpZGVvcywgc2V0VmlkZW9zXSA9IHVzZVN0YXRlPENvdXJzZVZpZGVvW10+KFtdKTtcclxuICBjb25zdCBbY3VycmVudFZpZGVvSW5kZXgsIHNldEN1cnJlbnRWaWRlb0luZGV4XSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2xlZnRTaWRlYmFyVmlzaWJsZSwgc2V0TGVmdFNpZGViYXJWaXNpYmxlXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtyaWdodFNpZGViYXJWaXNpYmxlLCBzZXRSaWdodFNpZGViYXJWaXNpYmxlXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtoYXNTdGFydGVkQ291cnNlLCBzZXRIYXNTdGFydGVkQ291cnNlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY291cnNlUHJvZ3Jlc3MsIHNldENvdXJzZVByb2dyZXNzXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XHJcbiAgY29uc3QgW21vZHVsZVByb2dyZXNzLCBzZXRNb2R1bGVQcm9ncmVzc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFtjdXJyZW50VXNlciwgc2V0Q3VycmVudFVzZXJdID0gdXNlU3RhdGU8YW55PihudWxsKTtcclxuICBjb25zdCBbaXNRbkFNb2RhbE9wZW4sIHNldElzUW5BTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcblxyXG4gIC8vIEdldCByZWFsIHVzZXIgSUQgZnJvbSBhdXRoZW50aWNhdGlvbiB0b2tlblxyXG4gIGNvbnN0IGdldFVzZXJGcm9tVG9rZW4gPSAoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgaWYgKHRva2VuKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgYmFzZTY0VXJsID0gdG9rZW4uc3BsaXQoJy4nKVsxXTtcclxuICAgICAgICBjb25zdCBiYXNlNjQgPSBiYXNlNjRVcmwucmVwbGFjZSgvLS9nLCAnKycpLnJlcGxhY2UoL18vZywgJy8nKTtcclxuICAgICAgICBjb25zdCBqc29uUGF5bG9hZCA9IGRlY29kZVVSSUNvbXBvbmVudChhdG9iKGJhc2U2NCkuc3BsaXQoJycpLm1hcChmdW5jdGlvbihjKSB7XHJcbiAgICAgICAgICByZXR1cm4gJyUnICsgKCcwMCcgKyBjLmNoYXJDb2RlQXQoMCkudG9TdHJpbmcoMTYpKS5zbGljZSgtMik7XHJcbiAgICAgICAgfSkuam9pbignJykpO1xyXG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKGpzb25QYXlsb2FkKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWNvZGluZyB0b2tlbjonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBudWxsO1xyXG4gIH07XHJcblxyXG4gIC8vIEluaXRpYWxpemUgdXNlciBmcm9tIHRva2VuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHVzZXIgPSBnZXRVc2VyRnJvbVRva2VuKCk7XHJcbiAgICBpZiAodXNlcikge1xyXG4gICAgICBzZXRDdXJyZW50VXNlcih1c2VyKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIFJlZGlyZWN0IHRvIGxvZ2luIGlmIG5vIHZhbGlkIHRva2VuXHJcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9zaWduaW4nO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgdXNlcklkID0gY3VycmVudFVzZXI/LmlkO1xyXG5cclxuICAvLyBGZXRjaCB1c2VyIHByb2dyZXNzXHJcbiAgY29uc3QgZmV0Y2hVc2VyUHJvZ3Jlc3MgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXVzZXJJZCkgcmV0dXJuOyAvLyBEb24ndCBmZXRjaCBpZiBubyB1c2VyIElEXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcHJvZ3Jlc3NSZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDEvYXBpL3VzZXJzLyR7dXNlcklkfS9jb3Vyc2VzLyR7Y291cnNlSWR9L3Byb2dyZXNzYCk7XHJcbiAgICAgIGlmIChwcm9ncmVzc1Jlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgcHJvZ3Jlc3NEYXRhID0gYXdhaXQgcHJvZ3Jlc3NSZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0Q291cnNlUHJvZ3Jlc3MocHJvZ3Jlc3NEYXRhLmNvdXJzZVByb2dyZXNzKTtcclxuICAgICAgICBzZXRNb2R1bGVQcm9ncmVzcyhwcm9ncmVzc0RhdGEubW9kdWxlUHJvZ3Jlc3MpO1xyXG5cclxuICAgICAgICAvLyBTZXQgY3VycmVudCB2aWRlbyBpbmRleCBiYXNlZCBvbiBwcm9ncmVzc1xyXG4gICAgICAgIGlmIChwcm9ncmVzc0RhdGEuY291cnNlUHJvZ3Jlc3MgJiYgcHJvZ3Jlc3NEYXRhLmNvdXJzZVByb2dyZXNzLmN1cnJlbnRfbW9kdWxlID4gMSkge1xyXG4gICAgICAgICAgc2V0Q3VycmVudFZpZGVvSW5kZXgocHJvZ3Jlc3NEYXRhLmNvdXJzZVByb2dyZXNzLmN1cnJlbnRfbW9kdWxlIC0gMSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9ncmVzczonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfSwgW3VzZXJJZCwgY291cnNlSWRdKTtcclxuXHJcbiAgLy8gRnVuY3Rpb24gdG8gc3RhcnQgY291cnNlICh0cmFjayBjb3Vyc2UgZW5yb2xsbWVudClcclxuICBjb25zdCBzdGFydENvdXJzZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIGlmICghdXNlcklkKSByZXR1cm47IC8vIERvbid0IHN0YXJ0IGlmIG5vIHVzZXIgSURcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDEvYXBpL2NvdXJzZXMvJHtjb3Vyc2VJZH0vc3RhcnRgLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgdXNlcklkIH0pLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHNldEhhc1N0YXJ0ZWRDb3Vyc2UodHJ1ZSk7XHJcbiAgICAgICAgYXdhaXQgZmV0Y2hVc2VyUHJvZ3Jlc3MoKTsgLy8gUmVmcmVzaCBwcm9ncmVzcyBkYXRhXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvdXJzZSBzdGFydGVkIGZvciBjb3Vyc2UgSUQ6JywgY291cnNlSWQpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdGFydGluZyBjb3Vyc2U6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VySWQsIGNvdXJzZUlkLCBmZXRjaFVzZXJQcm9ncmVzc10pO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byB1cGRhdGUgbW9kdWxlIHByb2dyZXNzXHJcbiAgY29uc3QgdXBkYXRlTW9kdWxlUHJvZ3Jlc3MgPSB1c2VDYWxsYmFjayhhc3luYyAobW9kdWxlTnVtYmVyOiBudW1iZXIsIHdhdGNoVGltZTogbnVtYmVyLCB0b3RhbER1cmF0aW9uOiBudW1iZXIsIGlzQ29tcGxldGVkOiBib29sZWFuID0gZmFsc2UpID0+IHtcclxuICAgIGlmICghdXNlcklkKSByZXR1cm47IC8vIERvbid0IHVwZGF0ZSBpZiBubyB1c2VyIElEXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgY3VycmVudFZpZGVvID0gdmlkZW9zW21vZHVsZU51bWJlciAtIDFdO1xyXG4gICAgICBjb25zdCByZXF1ZXN0RGF0YSA9IHtcclxuICAgICAgICB1c2VySWQsXHJcbiAgICAgICAgd2F0Y2hUaW1lLFxyXG4gICAgICAgIHRvdGFsRHVyYXRpb24sXHJcbiAgICAgICAgaXNDb21wbGV0ZWQsXHJcbiAgICAgICAgbW9kdWxlVGl0bGU6IGN1cnJlbnRWaWRlbz8ucGFnZV90aXRsZSB8fCBgTW9kdWxlICR7bW9kdWxlTnVtYmVyfWAsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnVXBkYXRpbmcgbW9kdWxlIHByb2dyZXNzOicsIHJlcXVlc3REYXRhKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMS9hcGkvY291cnNlcy8ke2NvdXJzZUlkfS9tb2R1bGVzLyR7bW9kdWxlTnVtYmVyfS9wcm9ncmVzc2AsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEpLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBjb25zb2xlLmxvZygnUHJvZ3Jlc3MgdXBkYXRlIHN1Y2Nlc3NmdWw6JywgcmVzdWx0KTtcclxuICAgICAgICBhd2FpdCBmZXRjaFVzZXJQcm9ncmVzcygpOyAvLyBSZWZyZXNoIHByb2dyZXNzIGRhdGFcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignUHJvZ3Jlc3MgdXBkYXRlIGZhaWxlZDonLCBlcnJvckRhdGEpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBtb2R1bGUgcHJvZ3Jlc3M6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VySWQsIGNvdXJzZUlkLCB2aWRlb3MsIGZldGNoVXNlclByb2dyZXNzXSk7XHJcblxyXG5cclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoQ291cnNlRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgICAvLyBGZXRjaCBjb3Vyc2UgZGV0YWlsc1xyXG4gICAgICAgIGNvbnN0IGNvdXJzZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMS9hcGkvY291cnNlcy8ke2NvdXJzZUlkfWApO1xyXG4gICAgICAgIGlmICghY291cnNlUmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGNvdXJzZSBkZXRhaWxzJyk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGNvdXJzZURhdGEgPSBhd2FpdCBjb3Vyc2VSZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0Q291cnNlKGNvdXJzZURhdGEpO1xyXG5cclxuICAgICAgICAvLyBGZXRjaCBjb3Vyc2UgdmlkZW9zXHJcbiAgICAgICAgY29uc3QgdmlkZW9zUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cDovL2xvY2FsaG9zdDo1MDAxL2FwaS9jb3Vyc2VzLyR7Y291cnNlSWR9L3ZpZGVvc2ApO1xyXG4gICAgICAgIGlmICghdmlkZW9zUmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGNvdXJzZSB2aWRlb3MnKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgdmlkZW9zRGF0YSA9IGF3YWl0IHZpZGVvc1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBzZXRWaWRlb3ModmlkZW9zRGF0YSk7XHJcblxyXG4gICAgICAgIC8vIEZldGNoIHVzZXIgcHJvZ3Jlc3MgZm9yIHRoaXMgY291cnNlIChvbmx5IGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZClcclxuICAgICAgICBpZiAodXNlcklkKSB7XHJcbiAgICAgICAgICBhd2FpdCBmZXRjaFVzZXJQcm9ncmVzcygpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgIH0gY2F0Y2ggKGVycjogdW5rbm93bikge1xyXG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpZiAoY291cnNlSWQgJiYgdXNlcklkKSB7XHJcbiAgICAgIGZldGNoQ291cnNlRGF0YSgpO1xyXG4gICAgfVxyXG4gIH0sIFtjb3Vyc2VJZCwgdXNlcklkXSk7XHJcblxyXG4gIC8vIEtleWJvYXJkIG5hdmlnYXRpb25cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZXZlbnQ6IEtleWJvYXJkRXZlbnQpID0+IHtcclxuICAgICAgaWYgKHZpZGVvcy5sZW5ndGggPT09IDApIHJldHVybjtcclxuXHJcbiAgICAgIGlmIChldmVudC5rZXkgPT09ICdBcnJvd0xlZnQnICYmIGN1cnJlbnRWaWRlb0luZGV4ID4gMCkge1xyXG4gICAgICAgIHNldEN1cnJlbnRWaWRlb0luZGV4KGN1cnJlbnRWaWRlb0luZGV4IC0gMSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZXZlbnQua2V5ID09PSAnQXJyb3dSaWdodCcgJiYgY3VycmVudFZpZGVvSW5kZXggPCB2aWRlb3MubGVuZ3RoIC0gMSkge1xyXG4gICAgICAgIHNldEN1cnJlbnRWaWRlb0luZGV4KGN1cnJlbnRWaWRlb0luZGV4ICsgMSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlQcmVzcyk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlQcmVzcyk7XHJcbiAgfSwgW2N1cnJlbnRWaWRlb0luZGV4LCB2aWRlb3MubGVuZ3RoXSk7XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuIGJnLVsjZmFmYmZjXVwiPlxyXG4gICAgICAgIDxOYXZiYXIgLz5cclxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LWdyb3cgY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIGNvdXJzZS4uLjwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9tYWluPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoZXJyb3IgfHwgIWNvdXJzZSkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1pbi1oLXNjcmVlbiBiZy1bI2ZhZmJmY11cIj5cclxuICAgICAgICA8TmF2YmFyIC8+XHJcbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93IGNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LXJlZC02MDBcIj5cclxuICAgICAgICAgICAgICB7ZXJyb3IgfHwgJ0NvdXJzZSBub3QgZm91bmQnfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvbWFpbj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgY3VycmVudFZpZGVvID0gdmlkZW9zW2N1cnJlbnRWaWRlb0luZGV4XTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxyXG4gICAgICA8TmF2YmFyIC8+XHJcbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtZ3Jvd1wiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLVtjYWxjKDEwMHZoLTY0cHgpXSByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgey8qIExlZnQgU2lkZWJhcjogQ291cnNlIEN1cnJpY3VsdW0gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7bGVmdFNpZGViYXJWaXNpYmxlID8gJ3ctODAnIDogJ3ctMCd9IGJnLXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBvdmVyZmxvdy1oaWRkZW5gfT5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTYwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMyA0YTEgMSAwIDAxMS0xaDEyYTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xVjR6bTAgNGExIDEgMCAwMTEtMWg2YTEgMSAwIDAxMSAxdjZhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xVjh6bTggMGExIDEgMCAwMTEtMWg0YTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxaC00YTEgMSAwIDAxLTEtMVY4em0wIDRhMSAxIDAgMDExLTFoNGExIDEgMCAwMTEgMXYyYTEgMSAwIDAxLTEgMWgtNGExIDEgMCAwMS0xLTF2LTJ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwXCI+Q291cnNlIEN1cnJpY3VsdW08L2gyPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIHt2aWRlb3MubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj57dmlkZW9zLmxlbmd0aH0gbW9kdWxlczwvcD5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgICAgICAgIHt2aWRlb3MubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt2aWRlb3MubWFwKCh2aWRlbywgaW5kZXgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtb2R1bGVQcm9ncmVzc0RhdGEgPSBtb2R1bGVQcm9ncmVzcy5maW5kKG1wID0+IG1wLm1vZHVsZV9udW1iZXIgPT09IHZpZGVvLnBhZ2VfbnVtYmVyKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0NvbXBsZXRlZCA9IG1vZHVsZVByb2dyZXNzRGF0YT8uaXNfY29tcGxldGVkIHx8IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRpb25QZXJjZW50YWdlID0gbW9kdWxlUHJvZ3Jlc3NEYXRhPy5jb21wbGV0aW9uX3BlcmNlbnRhZ2UgfHwgMDtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt2aWRlby5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIG1iLTIgcm91bmRlZC1sZyBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gY3VycmVudFZpZGVvSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWwtNCBib3JkZXItYmx1ZS01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdob3ZlcjpiZy1ncmF5LTUwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9ICR7aXNDb21wbGV0ZWQgPyAnYmctZ3JlZW4tNTAnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldEN1cnJlbnRWaWRlb0luZGV4KGluZGV4KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWhhc1N0YXJ0ZWRDb3Vyc2UpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJ0Q291cnNlKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgdy04IGgtOCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1zbSBmb250LXNlbWlib2xkICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBsZXRlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpbmRleCA9PT0gY3VycmVudFZpZGVvSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS02MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzQ29tcGxldGVkID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTYuNzA3IDUuMjkzYTEgMSAwIDAxMCAxLjQxNGwtOCA4YTEgMSAwIDAxLTEuNDE0IDBsLTQtNGExIDEgMCAwMTEuNDE0LTEuNDE0TDggMTIuNTg2bDcuMjkzLTcuMjkzYTEgMSAwIDAxMS40MTQgMHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBpbmRleCA9PT0gY3VycmVudFZpZGVvSW5kZXggPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2ek05LjU1NSA3LjE2OEExIDEgMCAwMDggOHY0YTEgMSAwIDAwMS41NTUuODMybDMtMmExIDEgMCAwMDAtMS42NjRsLTMtMnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZGVvLnBhZ2VfbnVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSB0ZXh0LXNtIGxlYWRpbmctdGlnaHQgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDb21wbGV0ZWQgPyAndGV4dC1ncmVlbi04MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IGN1cnJlbnRWaWRlb0luZGV4ID8gJ3RleHQtYmx1ZS04MDAnIDogJ3RleHQtZ3JheS04MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1vZHVsZSB7dmlkZW8ucGFnZV9udW1iZXJ9OiB7dmlkZW8ucGFnZV90aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDb21wbGV0ZWQgPyAndGV4dC1ncmVlbi02MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IGN1cnJlbnRWaWRlb0luZGV4ID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2aWRlby5hdmF0YXJfbmFtZX0g4oCiIDMwIG1pblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQteHMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBsZXRlZCA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID09PSBjdXJyZW50VmlkZW9JbmRleCA/ICd0ZXh0LWJsdWUtNjAwJyA6ICd0ZXh0LWdyYXktNTAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzQ29tcGxldGVkID8gJ0NvbXBsZXRlZCcgOiBjb21wbGV0aW9uUGVyY2VudGFnZSA+IDAgPyBgJHtNYXRoLnJvdW5kKGNvbXBsZXRpb25QZXJjZW50YWdlKX0lIHdhdGNoZWRgIDogJ05vdCBzdGFydGVkJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29tcGxldGlvblBlcmNlbnRhZ2UgPiAwICYmICFpc0NvbXBsZXRlZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBoLTEgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtjb21wbGV0aW9uUGVyY2VudGFnZX0lYCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgIH0pfVxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIFRha2UgRmluYWwgQXNzZXNzbWVudCAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG10LTQgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VQcm9ncmVzcz8ucHJvZ3Jlc3NfcGVyY2VudGFnZSA9PT0gMTAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAgaG92ZXI6YmctZ3JlZW4tMTAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTUwIGJvcmRlci1ncmF5LTIwMCBjdXJzb3Itbm90LWFsbG93ZWQgb3BhY2l0eS02MCdcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoY291cnNlUHJvZ3Jlc3M/LnByb2dyZXNzX3BlcmNlbnRhZ2UgPT09IDEwMCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93QXNzZXNzbWVudE1vZGFsKHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctOCBoLTggcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzPy5wcm9ncmVzc19wZXJjZW50YWdlID09PSAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPXtgdy00IGgtNCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzPy5wcm9ncmVzc19wZXJjZW50YWdlID09PSAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtZ3JlZW4tNjAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTQwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9IGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzPy5wcm9ncmVzc19wZXJjZW50YWdlID09PSAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtZ3JlZW4tODAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTUwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlRha2UgRmluYWwgQXNzZXNzbWVudDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQteHMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VQcm9ncmVzcz8ucHJvZ3Jlc3NfcGVyY2VudGFnZSA9PT0gMTAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LWdyZWVuLTYwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlUHJvZ3Jlc3M/LnByb2dyZXNzX3BlcmNlbnRhZ2UgPT09IDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnQ2xpY2sgdG8gc3RhcnQgYXNzZXNzbWVudCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ0NvbXBsZXRlIGFsbCBtb2R1bGVzIHRvIHVubG9jayBhc3Nlc3NtZW50J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtY2VudGVyIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgPHA+Tm8gY3VycmljdWx1bSBhdmFpbGFibGU8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBMZWZ0IFNpZGViYXIgVG9nZ2xlIEJ1dHRvbiAqL31cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TGVmdFNpZGViYXJWaXNpYmxlKCFsZWZ0U2lkZWJhclZpc2libGUpfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB6LTEwIGJnLXllbGxvdy00MDAgaG92ZXI6YmcteWVsbG93LTUwMCB0ZXh0LWJsYWNrIHAtMiByb3VuZGVkLXItbGcgc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgIHN0eWxlPXt7IGxlZnQ6IGxlZnRTaWRlYmFyVmlzaWJsZSA/ICczMjBweCcgOiAnMHB4JyB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy01IGgtNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDAgJHtsZWZ0U2lkZWJhclZpc2libGUgPyAncm90YXRlLTE4MCcgOiAnJ31gfVxyXG4gICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjAgMjBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk03LjI5MyAxNC43MDdhMSAxIDAgMDEwLTEuNDE0TDEwLjU4NiAxMCA3LjI5MyA2LjcwN2ExIDEgMCAwMTEuNDE0LTEuNDE0bDQgNGExIDEgMCAwMTAgMS40MTRsLTQgNGExIDEgMCAwMS0xLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgey8qIENlbnRlcjogVmlkZW8gUGxheWVyIGFuZCBDb250ZW50ICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxyXG4gICAgICAgICAgICB7LyogVmlkZW8gUGxheWVyICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsYWNrIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAge3ZpZGVvcy5sZW5ndGggPiAwICYmIGN1cnJlbnRWaWRlbyAmJiBjdXJyZW50VmlkZW8uaGV5Z2VuYmxvYnVybCA/IChcclxuICAgICAgICAgICAgICAgIDx2aWRlb1xyXG4gICAgICAgICAgICAgICAgICBrZXk9e2N1cnJlbnRWaWRlby5oZXlnZW5ibG9idXJsfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1bNDAwcHhdIG9iamVjdC1jb3ZlclwiXHJcbiAgICAgICAgICAgICAgICAgIGNvbnRyb2xzXHJcbiAgICAgICAgICAgICAgICAgIGF1dG9QbGF5XHJcbiAgICAgICAgICAgICAgICAgIG9uUGxheT17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICghaGFzU3RhcnRlZENvdXJzZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc3RhcnRDb3Vyc2UoKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIG9uVGltZVVwZGF0ZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2aWRlbyA9IGUudGFyZ2V0IGFzIEhUTUxWaWRlb0VsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgd2F0Y2hUaW1lID0gTWF0aC5mbG9vcih2aWRlby5jdXJyZW50VGltZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxEdXJhdGlvbiA9IE1hdGguZmxvb3IodmlkZW8uZHVyYXRpb24pO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgcHJvZ3Jlc3MgZXZlcnkgMTAgc2Vjb25kc1xyXG4gICAgICAgICAgICAgICAgICAgIGlmICh3YXRjaFRpbWUgJSAxMCA9PT0gMCAmJiB3YXRjaFRpbWUgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVNb2R1bGVQcm9ncmVzcyhjdXJyZW50VmlkZW8ucGFnZV9udW1iZXIsIHdhdGNoVGltZSwgdG90YWxEdXJhdGlvbik7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICBvbkVuZGVkPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmlkZW8gPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCd2aWRlbycpIGFzIEhUTUxWaWRlb0VsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZpZGVvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0b3RhbER1cmF0aW9uID0gTWF0aC5mbG9vcih2aWRlby5kdXJhdGlvbik7XHJcbiAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVNb2R1bGVQcm9ncmVzcyhjdXJyZW50VmlkZW8ucGFnZV9udW1iZXIsIHRvdGFsRHVyYXRpb24sIHRvdGFsRHVyYXRpb24sIHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gQXV0by1hZHZhbmNlIHRvIG5leHQgdmlkZW8gYWZ0ZXIgYSBzaG9ydCBkZWxheVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRWaWRlb0luZGV4IDwgdmlkZW9zLmxlbmd0aCAtIDEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFZpZGVvSW5kZXgoY3VycmVudFZpZGVvSW5kZXggKyAxKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9LCAxNTAwKTsgLy8gMS41IHNlY29uZCBkZWxheSBiZWZvcmUgYXV0by1hZHZhbmNpbmdcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPHNvdXJjZSBzcmM9e2N1cnJlbnRWaWRlby5oZXlnZW5ibG9idXJsfSB0eXBlPVwidmlkZW8vbXA0XCIgLz5cclxuICAgICAgICAgICAgICAgICAgWW91ciBicm93c2VyIGRvZXMgbm90IHN1cHBvcnQgdGhlIHZpZGVvIHRhZy5cclxuICAgICAgICAgICAgICAgIDwvdmlkZW8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtWzQwMHB4XSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICB7dmlkZW9zLmxlbmd0aCA9PT0gMCA/ICdObyB2aWRlb3MgYXZhaWxhYmxlIGZvciB0aGlzIGNvdXJzZScgOiAnTG9hZGluZyB2aWRlby4uLid9XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBIYW5kIFJhaXNlIEljb24gT3ZlcmxheSAqL31cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1FuQU1vZGFsT3Blbih0cnVlKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IHJpZ2h0LTQgYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDAgdGV4dC13aGl0ZSBwLTMgcm91bmRlZC1mdWxsIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTExMCBncm91cCB6LTEwXCJcclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiUmFpc2UgSGFuZFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IGdyb3VwLWhvdmVyOmFuaW1hdGUtYm91bmNlXCJcclxuICAgICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjAgMjBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTkgM2ExIDEgMCAwMTIgMHY1LjVhLjUuNSAwIDAwMSAwVjRhMSAxIDAgMTEyIDB2NC41YS41LjUgMCAwMDEgMFY2YTEgMSAwIDExMiAwdjZhNyA3IDAgMTEtMTQgMFY5YTEgMSAwIDAxMiAwdjIuNWEuNS41IDAgMDAxIDBWNGExIDEgMCAwMTIgMHY0LjVhLjUuNSAwIDAwMSAwVjN6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIvPlxyXG4gICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFRhYnMgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtOCBweC02XCI+XHJcbiAgICAgICAgICAgICAgICB7WydPdmVydmlldycsICdBc3Nlc3NtZW50JywgJ0Rpc2N1c3Npb24nLCAnUmV2aWV3cyddLm1hcCgodGFiKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBrZXk9e3RhYn1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS00IGZvbnQtbWVkaXVtIGJvcmRlci1iLTIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gdGFiICYmIHRhYiAhPT0gJ0Fzc2Vzc21lbnQnICYmIHRhYiAhPT0gJ0Rpc2N1c3Npb24nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1vcmFuZ2UtNTAwIHRleHQtb3JhbmdlLTYwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCdcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZiAodGFiID09PSAnQXNzZXNzbWVudCcpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0Fzc2Vzc21lbnRNb2RhbCh0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGFiID09PSAnRGlzY3Vzc2lvbicpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goYC9jb3Vyc2VzLyR7Y291cnNlSWR9L2Rpc2N1c3Npb25gKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEFjdGl2ZVRhYih0YWIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7dGFifVxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgYmctd2hpdGUgcC02IG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdPdmVydmlldycgJiYgKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi00XCI+QWJvdXQgVGhpcyBDb3Vyc2U8L2gyPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICBDb21wcmVoZW5zaXZlIHRyYWluaW5nIGluIGNhcmRpb3Zhc2N1bGFyIG1lZGljaW5lIGNvdmVyaW5nIGRpYWdub3NpcywgdHJlYXRtZW50LCBhbmQgcGF0aWVudCBjYXJlIHByb3RvY29scy5cclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItM1wiPldoYXQgeW91J2xsIGxlYXJuPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1ncmF5LTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIG10LTFcIj7igKI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgSW5kdXN0cnkgYmVzdCBwcmFjdGljZXMgYW5kIHN0YW5kYXJkc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIG10LTFcIj7igKI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJhY3RpY2FsIGltcGxlbWVudGF0aW9uIHN0cmF0ZWdpZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTUwMCBtdC0xXCI+4oCiPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFJlYWwtd29ybGQgY2FzZSBzdHVkaWVzIGFuZCBleGFtcGxlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIG10LTFcIj7igKI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJvZmVzc2lvbmFsIGNlcnRpZmljYXRpb24gcHJlcGFyYXRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTNcIj5QcmVyZXF1aXNpdGVzPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBBZHZhbmNlZCBrbm93bGVkZ2UgYW5kIGV4cGVyaWVuY2UgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTNcIj5Db3Vyc2UgRGV0YWlsczwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0xIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPkRvbWFpbjoge2NvdXJzZS5kb21haW59PC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPkxldmVsOiB7Y291cnNlLmxldmVsfTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5EdXJhdGlvbjoge2NvdXJzZS5lc3RpbWF0ZWRfaG91cnN9IGhvdXJzPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPk1vZHVsZXM6IHt2aWRlb3MubGVuZ3RofTwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5DZXJ0aWZpY2F0ZTogQXZhaWxhYmxlIHVwb24gY29tcGxldGlvbjwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG5cclxuXHJcbiAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ0Rpc2N1c3Npb24nICYmIChcclxuICAgICAgICAgICAgICAgIHVzZXJJZCA/IChcclxuICAgICAgICAgICAgICAgICAgPERpc2N1c3Npb25Gb3J1bSBjb3Vyc2VJZD17Y291cnNlSWR9IHVzZXJJZD17dXNlcklkfSAvPlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlBsZWFzZSBsb2cgaW4gdG8gYWNjZXNzIHRoZSBkaXNjdXNzaW9uIGZvcnVtLjwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ1Jldmlld3MnICYmIChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LWNlbnRlciBweS04XCI+U3R1ZGVudCByZXZpZXdzIGNvbWluZyBzb29uLi4uPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogUmlnaHQgU2lkZWJhciBUb2dnbGUgQnV0dG9uICovfVxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRSaWdodFNpZGViYXJWaXNpYmxlKCFyaWdodFNpZGViYXJWaXNpYmxlKX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHotMTAgYmcteWVsbG93LTQwMCBob3ZlcjpiZy15ZWxsb3ctNTAwIHRleHQtYmxhY2sgcC0yIHJvdW5kZWQtbC1sZyBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgc3R5bGU9e3sgcmlnaHQ6IHJpZ2h0U2lkZWJhclZpc2libGUgPyAnMzIwcHgnIDogJzBweCcgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctNSBoLTUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwICR7cmlnaHRTaWRlYmFyVmlzaWJsZSA/ICcnIDogJ3JvdGF0ZS0xODAnfWB9XHJcbiAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMCAyMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTcuMjkzIDE0LjcwN2ExIDEgMCAwMTAtMS40MTRMMTAuNTg2IDEwIDcuMjkzIDYuNzA3YTEgMSAwIDAxMS40MTQtMS40MTRsNCA0YTEgMSAwIDAxMCAxLjQxNGwtNCA0YTEgMSAwIDAxLTEuNDE0IDB6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICB7LyogUmlnaHQgU2lkZWJhcjogQ291cnNlIEluZm8gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7cmlnaHRTaWRlYmFyVmlzaWJsZSA/ICd3LTgwJyA6ICd3LTAnfSBiZy13aGl0ZSBib3JkZXItbCBib3JkZXItZ3JheS0yMDAgZmxleCBmbGV4LWNvbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgb3ZlcmZsb3ctaGlkZGVuYH0+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgbWItMlwiPkNvdXJzZSBJbmZvPC9oMz5cclxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0zXCI+e2NvdXJzZS50aXRsZX08L2gyPlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgIHtjb3Vyc2UuZG9tYWlufVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IHRleHQtc20gdGV4dC1ncmF5LTYwMCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOS4wNDkgMi45MjdjLjMtLjkyMSAxLjYwMy0uOTIxIDEuOTAyIDBsMS4wNyAzLjI5MmExIDEgMCAwMC45NS42OWgzLjQ2MmMuOTY5IDAgMS4zNzEgMS4yNC41ODggMS44MWwtMi44IDIuMDM0YTEgMSAwIDAwLS4zNjQgMS4xMThsMS4wNyAzLjI5MmMuMy45MjEtLjc1NSAxLjY4OC0xLjU0IDEuMTE4bC0yLjgtMi4wMzRhMSAxIDAgMDAtMS4xNzUgMGwtMi44IDIuMDM0Yy0uNzg0LjU3LTEuODM4LS4xOTctMS41MzktMS4xMThsMS4wNy0zLjI5MmExIDEgMCAwMC0uMzY0LTEuMTE4TDIuOTggOC43MmMtLjc4My0uNTctLjM4LTEuODEuNTg4LTEuODFoMy40NjFhMSAxIDAgMDAuOTUxLS42OWwxLjA3LTMuMjkyelwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPjQuODwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+KDIzNCByZXZpZXdzKTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnptMS0xMmExIDEgMCAxMC0yIDB2NGExIDEgMCAwMC4yOTMuNzA3bDIuODI4IDIuODI5YTEgMSAwIDEwMS40MTUtMS40MTVMMTEgOS41ODZWNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+e2NvdXJzZS5lc3RpbWF0ZWRfaG91cnN9IGhvdXJzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMyA2YTMgMyAwIDExLTYgMCAzIDMgMCAwMTYgMHpNMTggOGEyIDIgMCAxMS00IDAgMiAyIDAgMDE0IDB6TTE0IDE1YTQgNCAwIDAwLTggMHYzaDh2LTN6XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPnt2aWRlb3MubGVuZ3RofSBzdHVkZW50czwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMyA0YTEgMSAwIDAxMS0xaDEyYTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xVjR6bTAgNGExIDEgMCAwMTEtMWg2YTEgMSAwIDAxMSAxdjZhMSAxIDAgMDEtMSAxSDRhMSAxIDAgMDEtMS0xVjh6bTggMGExIDEgMCAwMTEtMWg0YTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxaC00YTEgMSAwIDAxLTEtMVY4em0wIDRhMSAxIDAgMDExLTFoNGExIDEgMCAwMTEgMXYyYTEgMSAwIDAxLTEgMWgtNGExIDEgMCAwMS0xLTF2LTJ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2UubGV2ZWx9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk02LjI2NyAzLjQ1NWEzLjA2NiAzLjA2NiAwIDAwMS43NDUtLjcyMyAzLjA2NiAzLjA2NiAwIDAxMy45NzYgMCAzLjA2NiAzLjA2NiAwIDAwMS43NDUuNzIzIDMuMDY2IDMuMDY2IDAgMDEyLjgxMiAyLjgxMmMuMDUxLjY0My4zMDQgMS4yNTQuNzIzIDEuNzQ1YTMuMDY2IDMuMDY2IDAgMDEwIDMuOTc2IDMuMDY2IDMuMDY2IDAgMDAtLjcyMyAxLjc0NSAzLjA2NiAzLjA2NiAwIDAxLTIuODEyIDIuODEyIDMuMDY2IDMuMDY2IDAgMDAtMS43NDUuNzIzIDMuMDY2IDMuMDY2IDAgMDEtMy45NzYgMCAzLjA2NiAzLjA2NiAwIDAwLTEuNzQ1LS43MjMgMy4wNjYgMy4wNjYgMCAwMS0yLjgxMi0yLjgxMiAzLjA2NiAzLjA2NiAwIDAwLS43MjMtMS43NDUgMy4wNjYgMy4wNjYgMCAwMTAtMy45NzYgMy4wNjYgMy4wNjYgMCAwMC43MjMtMS43NDUgMy4wNjYgMy4wNjYgMCAwMTIuODEyLTIuODEyem03LjQ0IDUuMjUyYTEgMSAwIDAwLTEuNDE0LTEuNDE0TDkgMTAuNTg2IDcuNzA3IDkuMjkzYTEgMSAwIDAwLTEuNDE0IDEuNDE0bDIgMmExIDEgMCAwMDEuNDE0IDBsNC00elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj5DZXJ0aWZpY2F0ZTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLW9yYW5nZS01MDAgaG92ZXI6Ymctb3JhbmdlLTYwMCB0ZXh0LXdoaXRlIHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIG1iLTMgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBpZiAoIWhhc1N0YXJ0ZWRDb3Vyc2UpIHtcclxuICAgICAgICAgICAgICAgICAgICBzdGFydENvdXJzZSgpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIFN0YXJ0IENvdXJzZVxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHRleHQtZ3JheS03MDAgcHktMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cclxuICAgICAgICAgICAgICAgIEFkZCB0byBXaXNobGlzdFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPiAqL31cclxuXHJcblxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiBDdXJyZW50IFByb2dyZXNzICovfVxyXG4gICAgICAgICAgICB7dmlkZW9zLmxlbmd0aCA+IDAgJiYgY291cnNlUHJvZ3Jlc3MgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIG1iLTNcIj5Db3Vyc2UgUHJvZ3Jlc3M8L2g0PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5PdmVyYWxsIFByb2dyZXNzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LW9yYW5nZS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtNYXRoLnJvdW5kKGNvdXJzZVByb2dyZXNzLnByb2dyZXNzX3BlcmNlbnRhZ2UpfSVcclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0yIG1iLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtjb3Vyc2VQcm9ncmVzcy5wcm9ncmVzc19wZXJjZW50YWdlfSVgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwPntjb3Vyc2VQcm9ncmVzcy5jb21wbGV0ZWRfbW9kdWxlc30gb2Yge2NvdXJzZVByb2dyZXNzLnRvdGFsX21vZHVsZXN9IG1vZHVsZXMgY29tcGxldGVkPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwPlN0YXR1czogPHNwYW4gY2xhc3NOYW1lPXtgZm9udC1tZWRpdW0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAndGV4dC1ncmVlbi02MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzLnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJyA/ICd0ZXh0LWJsdWUtNjAwJyA6ICd0ZXh0LWdyYXktNjAwJ1xyXG4gICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VQcm9ncmVzcy5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ0NvbXBsZXRlZCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzLnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJyA/ICdJbiBQcm9ncmVzcycgOiAnTm90IFN0YXJ0ZWQnfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj48L3A+XHJcbiAgICAgICAgICAgICAgICAgICAge2NvdXJzZVByb2dyZXNzLnN0YXJ0ZWRfYXQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHA+U3RhcnRlZDoge25ldyBEYXRlKGNvdXJzZVByb2dyZXNzLnN0YXJ0ZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VQcm9ncmVzcy5jb21wbGV0ZWRfYXQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHA+Q29tcGxldGVkOiB7bmV3IERhdGUoY291cnNlUHJvZ3Jlc3MuY29tcGxldGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogQ3VycmVudCBNb2R1bGUgSW5mbyAqL31cclxuICAgICAgICAgICAgICAgIHtjdXJyZW50VmlkZW8gJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcC0zIGJnLWJsdWUtNTAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDAgbWItMVwiPkN1cnJlbnRseSBXYXRjaGluZzwvaDU+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBNb2R1bGUge2N1cnJlbnRWaWRlby5wYWdlX251bWJlcn06IHtjdXJyZW50VmlkZW8ucGFnZV90aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VmlkZW8uYXZhdGFyX25hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9tYWluPlxyXG5cclxuICAgICAgey8qIFFuQSB3aXRoIEhleUdlbiBNb2RhbCAqL31cclxuICAgICAgPFFuQVdpdGhIZXlHZW5Nb2RhbFxyXG4gICAgICAgIGlzT3Blbj17aXNRbkFNb2RhbE9wZW59XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNRbkFNb2RhbE9wZW4oZmFsc2UpfVxyXG4gICAgICAgIGNvdXJzZUlkPXtjb3Vyc2VJZH1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiBBc3Nlc3NtZW50IE1vZGFsICovfVxyXG4gICAgICA8QXNzZXNzbWVudE1vZGFsXHJcbiAgICAgICAgaXNPcGVuPXtzaG93QXNzZXNzbWVudE1vZGFsfVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dBc3Nlc3NtZW50TW9kYWwoZmFsc2UpfVxyXG4gICAgICAgIGNvdXJzZUlkPXtjb3Vyc2VJZH1cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiTmF2YmFyIiwiRGlzY3Vzc2lvbkZvcnVtIiwiUW5BV2l0aEhleUdlbk1vZGFsIiwiQXNzZXNzbWVudE1vZGFsIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwiQ291cnNlVmlld1BhZ2UiLCJwYXJhbXMiLCJyb3V0ZXIiLCJjb3Vyc2VJZCIsImlkIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic2hvd0Fzc2Vzc21lbnRNb2RhbCIsInNldFNob3dBc3Nlc3NtZW50TW9kYWwiLCJjb3Vyc2UiLCJzZXRDb3Vyc2UiLCJ2aWRlb3MiLCJzZXRWaWRlb3MiLCJjdXJyZW50VmlkZW9JbmRleCIsInNldEN1cnJlbnRWaWRlb0luZGV4IiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibGVmdFNpZGViYXJWaXNpYmxlIiwic2V0TGVmdFNpZGViYXJWaXNpYmxlIiwicmlnaHRTaWRlYmFyVmlzaWJsZSIsInNldFJpZ2h0U2lkZWJhclZpc2libGUiLCJoYXNTdGFydGVkQ291cnNlIiwic2V0SGFzU3RhcnRlZENvdXJzZSIsImNvdXJzZVByb2dyZXNzIiwic2V0Q291cnNlUHJvZ3Jlc3MiLCJtb2R1bGVQcm9ncmVzcyIsInNldE1vZHVsZVByb2dyZXNzIiwiY3VycmVudFVzZXIiLCJzZXRDdXJyZW50VXNlciIsImlzUW5BTW9kYWxPcGVuIiwic2V0SXNRbkFNb2RhbE9wZW4iLCJnZXRVc2VyRnJvbVRva2VuIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiYmFzZTY0VXJsIiwic3BsaXQiLCJiYXNlNjQiLCJyZXBsYWNlIiwianNvblBheWxvYWQiLCJkZWNvZGVVUklDb21wb25lbnQiLCJhdG9iIiwibWFwIiwiYyIsImNoYXJDb2RlQXQiLCJ0b1N0cmluZyIsInNsaWNlIiwiam9pbiIsIkpTT04iLCJwYXJzZSIsImNvbnNvbGUiLCJ1c2VyIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwidXNlcklkIiwiZmV0Y2hVc2VyUHJvZ3Jlc3MiLCJwcm9ncmVzc1Jlc3BvbnNlIiwiZmV0Y2giLCJvayIsInByb2dyZXNzRGF0YSIsImpzb24iLCJjdXJyZW50X21vZHVsZSIsInN0YXJ0Q291cnNlIiwicmVzcG9uc2UiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInN0cmluZ2lmeSIsImxvZyIsInVwZGF0ZU1vZHVsZVByb2dyZXNzIiwibW9kdWxlTnVtYmVyIiwid2F0Y2hUaW1lIiwidG90YWxEdXJhdGlvbiIsImlzQ29tcGxldGVkIiwiY3VycmVudFZpZGVvIiwicmVxdWVzdERhdGEiLCJtb2R1bGVUaXRsZSIsInBhZ2VfdGl0bGUiLCJyZXN1bHQiLCJlcnJvckRhdGEiLCJmZXRjaENvdXJzZURhdGEiLCJjb3Vyc2VSZXNwb25zZSIsIkVycm9yIiwiY291cnNlRGF0YSIsInZpZGVvc1Jlc3BvbnNlIiwidmlkZW9zRGF0YSIsImVyciIsIm1lc3NhZ2UiLCJoYW5kbGVLZXlQcmVzcyIsImV2ZW50IiwibGVuZ3RoIiwia2V5IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJoMiIsInAiLCJ2aWRlbyIsImluZGV4IiwibW9kdWxlUHJvZ3Jlc3NEYXRhIiwiZmluZCIsIm1wIiwibW9kdWxlX251bWJlciIsInBhZ2VfbnVtYmVyIiwiaXNfY29tcGxldGVkIiwiY29tcGxldGlvblBlcmNlbnRhZ2UiLCJjb21wbGV0aW9uX3BlcmNlbnRhZ2UiLCJvbkNsaWNrIiwiaDMiLCJhdmF0YXJfbmFtZSIsIk1hdGgiLCJyb3VuZCIsInN0eWxlIiwid2lkdGgiLCJwcm9ncmVzc19wZXJjZW50YWdlIiwiYnV0dG9uIiwibGVmdCIsImhleWdlbmJsb2J1cmwiLCJjb250cm9scyIsImF1dG9QbGF5Iiwib25QbGF5Iiwib25UaW1lVXBkYXRlIiwiZSIsInRhcmdldCIsImZsb29yIiwiY3VycmVudFRpbWUiLCJkdXJhdGlvbiIsIm9uRW5kZWQiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJzZXRUaW1lb3V0Iiwic291cmNlIiwic3JjIiwidHlwZSIsInNwYW4iLCJ0aXRsZSIsInRhYiIsInB1c2giLCJ1bCIsImxpIiwiZG9tYWluIiwibGV2ZWwiLCJlc3RpbWF0ZWRfaG91cnMiLCJyaWdodCIsImg0IiwiY29tcGxldGVkX21vZHVsZXMiLCJ0b3RhbF9tb2R1bGVzIiwic3RhdHVzIiwic3RhcnRlZF9hdCIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJjb21wbGV0ZWRfYXQiLCJoNSIsImlzT3BlbiIsIm9uQ2xvc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/page.tsx\n"));

/***/ })

});