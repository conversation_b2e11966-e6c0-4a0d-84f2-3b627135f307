"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/avatar-video/page",{

/***/ "(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AvatarVideoPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AvatarVideoPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clapperboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AvatarVideoPanel() {\n    var _avatars_find, _uniqueCourses_find, _filteredVersions_find;\n    _s();\n    const [avatars, setAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedAvatarId, setSelectedAvatarId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [approvedVersions, setApprovedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedCourseIdForVideo, setSelectedCourseIdForVideo] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [selectedVersionId, setSelectedVersionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [loadingVersions, setLoadingVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [loadingAvatars, setLoadingAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [generatingVideos, setGeneratingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [videoGenerationResults, setVideoGenerationResults] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({\n        current: 0,\n        total: 0\n    });\n    const [currentGeneratingPage, setCurrentGeneratingPage] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            const fetchApprovedVersions = {\n                \"AvatarVideoPanel.useEffect.fetchApprovedVersions\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/all-approved-content-versions\");\n                        const data = await res.json();\n                        setApprovedVersions(data);\n                    } catch (err) {\n                        console.error(\"Failed to fetch approved versions:\", err);\n                        alert(\"Failed to load approved content versions.\");\n                    } finally{\n                        setLoadingVersions(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchApprovedVersions\"];\n            const fetchAvatars = {\n                \"AvatarVideoPanel.useEffect.fetchAvatars\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/avatars\");\n                        const data = await res.json();\n                        if (Array.isArray(data)) {\n                            setAvatars(data);\n                        } else {\n                            console.error(\"API returned non-array data for avatars:\", data);\n                            setAvatars([]);\n                            alert(\"Failed to load avatar configurations: Invalid data format.\");\n                        }\n                    } catch (err) {\n                        console.error(\"Failed to fetch avatars:\", err);\n                        alert(\"Failed to load avatar configurations. Please ensure backend is running.\");\n                    } finally{\n                        setLoadingAvatars(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchAvatars\"];\n            fetchApprovedVersions();\n            fetchAvatars();\n        }\n    }[\"AvatarVideoPanel.useEffect\"], []);\n    const uniqueCourses = approvedVersions.reduce((acc, version)=>{\n        if (!acc.find((course)=>course.course_id === version.course_id)) {\n            acc.push({\n                course_id: version.course_id,\n                course_title: version.course_title\n            });\n        }\n        return acc;\n    }, []);\n    const filteredVersions = approvedVersions.filter((version)=>version.course_id === Number(selectedCourseIdForVideo));\n    const handleGenerateVideosClick = ()=>{\n        if (!selectedAvatarId || !selectedVersionId) {\n            alert(\"Please select both an avatar and a content version.\");\n            return;\n        }\n        setShowConfirmDialog(true);\n    };\n    const handleConfirmGeneration = async ()=>{\n        setShowConfirmDialog(false);\n        setGeneratingVideos(true);\n        setVideoGenerationResults([]);\n        setGenerationProgress({\n            current: 0,\n            total: 0\n        });\n        setCurrentGeneratingPage('');\n        try {\n            // First get the content to know total pages\n            const versionRes = await fetch(\"http://localhost:5001/api/course-content-versions/\".concat(selectedVersionId));\n            const versionData = await versionRes.json();\n            const totalPages = Array.isArray(versionData.content) ? versionData.content.length : 0;\n            setGenerationProgress({\n                current: 0,\n                total: totalPages\n            });\n            const res = await fetch(\"http://localhost:5001/api/generate-course-videos\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    selectedAvatarId,\n                    selectedVersionId\n                })\n            });\n            const result = await res.json();\n            if (!res.ok) {\n                throw new Error(result.error || \"Failed to initiate video generation\");\n            }\n            console.log(\"Generated Videos Data:\", result.generated_videos);\n            setVideoGenerationResults(result.generated_videos || []);\n        } catch (error) {\n            console.error(\"Video generation error:\", error);\n            alert(\"Video generation failed: \".concat(error.message || \"Unknown error\"));\n        } finally{\n            setGeneratingVideos(false);\n            setCurrentGeneratingPage('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent\",\n                                    children: \"Medical Course Avatar & Video Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                            className: \"my-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedAvatarId ? 'border-blue-500 bg-blue-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedAvatarId ? 'bg-blue-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedAvatarId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedAvatarId ? 'text-blue-600' : 'text-gray-500'),\n                                                        children: \"Select Avatar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose your presenter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedVersionId ? 'border-green-500 bg-green-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedVersionId ? 'bg-green-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedVersionId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedVersionId ? 'text-green-600' : 'text-gray-500'),\n                                                        children: \"Select Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose course material\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(generatingVideos ? 'border-amber-500 bg-amber-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(generatingVideos ? 'bg-amber-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(generatingVideos ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(generatingVideos ? 'text-amber-600' : 'text-gray-500'),\n                                                        children: \"Generate Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Create content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Medical Professional Avatar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your video presenter from our collection of medical professionals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingAvatars ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading avatars...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this) : avatars.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No avatars available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: avatars.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group relative cursor-pointer transition-all duration-300 hover:shadow-xl \".concat(selectedAvatarId === a.id ? \"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105\" : \"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102\"),\n                                    onClick: ()=>setSelectedAvatarId(a.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        style: {\n                                            minHeight: 280\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all \".concat(selectedAvatarId === a.id ? 'bg-blue-500 shadow-lg' : 'bg-orange-100 group-hover:bg-blue-100'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-8 h-8 transition-colors \".concat(selectedAvatarId === a.id ? 'text-white' : 'text-orange-600 group-hover:text-blue-600')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-gray-900 mb-3 text-lg truncate\",\n                                                title: a.avatar_name,\n                                                children: a.avatar_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: selectedAvatarId === a.id ? \"default\" : \"outline\",\n                                                className: \"w-full transition-all duration-200 \".concat(selectedAvatarId === a.id ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-md\" : \"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300\"),\n                                                children: selectedAvatarId === a.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 25\n                                                }, this) : \"Select Avatar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 19\n                                    }, this)\n                                }, a.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            selectedAvatarId !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-7 h-7 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Course Content for Video Generation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your course and approved version for video creation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingVersions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading approved content versions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this) : approvedVersions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCDA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No approved content versions available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"course\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Course Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedCourseIdForVideo || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedCourseIdForVideo(value);\n                                                setSelectedVersionId(null);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Choose a course to generate videos from\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: uniqueCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: course.course_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: course.course_title\n                                                        }, course.course_id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCourseIdForVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"version\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Version Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedVersionId || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedVersionId(value);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select an approved version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: filteredVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: version.version_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number,\n                                                                \" (Approved: \",\n                                                                new Date(version.approved_at).toLocaleDateString(),\n                                                                \")\"\n                                                            ]\n                                                        }, version.version_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-7 h-7 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-gray-900\",\n                                                    children: \"Video Generation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-1\",\n                                                    children: \"Generate professional video content from your selections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: 'warning',\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    onClick: handleGenerateVideosClick,\n                                    disabled: !selectedAvatarId || !selectedVersionId || generatingVideos,\n                                    children: generatingVideos ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin w-6 h-6 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generating Videos...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generate All Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            generatingVideos && generationProgress.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 text-lg flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generation Progress\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"bg-blue-100 text-blue-800 font-semibold\",\n                                                    children: [\n                                                        generationProgress.current,\n                                                        \" of \",\n                                                        generationProgress.total,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: generationProgress.current / generationProgress.total * 100,\n                                            className: \"h-3 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentGeneratingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Currently generating: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium ml-1\",\n                                                    children: currentGeneratingPage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length === 0 && !generatingVideos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Ready to Generate Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed\",\n                                        children: 'Select an avatar and course content, then click \"Generate All Videos\" to begin creating your professional video content with AI.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    (!selectedAvatarId || !selectedVersionId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-amber-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Selection Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-amber-600\",\n                                                    children: \"Please select both an avatar and course content to proceed with video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedAvatarId && selectedVersionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-green-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Ready to Generate!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Avatar and content selected. Click the button above to start video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-gray-800 text-xl\",\n                                                children: \"Generated Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: [\n                                                    videoGenerationResults.length,\n                                                    \" videos processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4\",\n                                        children: videoGenerationResults.map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-4\",\n                                                                    children: [\n                                                                        page.generation_status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 31\n                                                                        }, this) : page.generation_status === \"skipped_empty_script\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-red-500 rounded-full mr-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-bold text-gray-800 text-xl\",\n                                                                            children: page.title || \"Chapter \".concat(page.page)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        \"Page \",\n                                                                        page.page\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                page.generation_status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                                                                    children: \"✅ Generated Successfully\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"skipped_empty_script\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                                    children: \"⚠️ Skipped (Empty Script)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-red-100 text-red-800 hover:bg-red-100\",\n                                                                            children: \"❌ Generation Failed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200\",\n                                                                            children: page.error_details || \"Unknown error occurred during generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showConfirmDialog,\n                onOpenChange: setShowConfirmDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                    children: \"Generate All Videos?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6 text-lg leading-relaxed\",\n                                        children: [\n                                            \"This will generate videos for all pages in the selected course content.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 88\n                                            }, this),\n                                            \"This process may take several minutes to complete.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center font-bold mb-4 text-amber-900 text-lg\",\n                                        children: \"Selected Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Avatar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_avatars_find = avatars.find((a)=>a.id === selectedAvatarId)) === null || _avatars_find === void 0 ? void 0 : _avatars_find.avatar_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_uniqueCourses_find = uniqueCourses.find((c)=>c.course_id === Number(selectedCourseIdForVideo))) === null || _uniqueCourses_find === void 0 ? void 0 : _uniqueCourses_find.course_title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_filteredVersions_find = filteredVersions.find((v)=>v.version_id === selectedVersionId)) === null || _filteredVersions_find === void 0 ? void 0 : _filteredVersions_find.version_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: ()=>setShowConfirmDialog(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"brand\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: handleConfirmGeneration,\n                                    children: \"Yes, Generate Videos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(AvatarVideoPanel, \"wuqIInURL/k/xt0WuAuzcdPe5hM=\");\n_c = AvatarVideoPanel;\nvar _c;\n$RefreshReg$(_c, \"AvatarVideoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx\n"));

/***/ })

});