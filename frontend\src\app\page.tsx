'use client';

import Navbar from '../components/Navbar';
import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faClock,
  faGraduationCap,
  faPlay,
  faChevronRight,
  faBookOpen,
  faUsers,
  faStar
} from '@fortawesome/free-solid-svg-icons';

interface Course {
  id: number;
  title: string;
  description: string;
  domain: string;
  level: string;
  estimated_hours: number;
  created_at: string;
}

interface CoursesByDomain {
  [domain: string]: Course[];
}

const CourseCard: React.FC<{ course: Course }> = ({ course }) => {
  const getPlaceholderImage = (domain: string) => {
    const domainImages: { [key: string]: string } = {
      'Healthcare & Medicine': '/python-thumb.png', // Using available image as fallback
      'Insurance & Risk Management': '/react-thumb.png',
      'Science': '/ui-ux-thumb.png',
      'Technology': '/react-thumb.png',
      'Engineering': '/python-thumb.png',
      'Mathematics': '/ui-ux-thumb.png',
      'Web Development': '/react-thumb.png',
      'Data Science': '/python-thumb.png',
      'Design': '/ui-ux-thumb.png',
      'Business': '/react-thumb.png',
      'Finance': '/python-thumb.png'
    };
    return domainImages[domain] || '/python-thumb.png'; // Default to available image
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-700';
      case 'intermediate': return 'bg-yellow-100 text-yellow-700';
      case 'advanced': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 group">
      <div className="relative">
        <div className="w-full h-40 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <FontAwesomeIcon icon={faGraduationCap} className="text-white text-2xl" />
            </div>
            <p className="text-blue-700 text-sm font-medium">{course.domain}</p>
          </div>
        </div>
        <div className="absolute inset-0 bg-blue-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="bg-white rounded-full p-3 shadow-lg">
              <FontAwesomeIcon icon={faPlay} className="text-blue-500 text-xl" />
            </div>
          </div>
        </div>
        <span className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
          {course.level}
        </span>
      </div>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{course.title}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{course.description}</p>
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-1" />
              {course.estimated_hours}h
            </span>
            <span className="flex items-center">
              <FontAwesomeIcon icon={faBookOpen} className="mr-1" />
              Course
            </span>
          </div>
          {/* <div className="flex items-center">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400 mr-1" />
            <span>4.8</span>
          </div> */}
        </div>
        <a
          href={`/courses/${course.id}`}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors duration-200 text-sm font-medium flex items-center justify-center group"
        >
          View Course
          <FontAwesomeIcon icon={faChevronRight} className="ml-2 text-xs group-hover:translate-x-1 transition-transform duration-200" />
        </a>
      </div>
    </div>
  );
};

export default function Home() {
  const [coursesByDomain, setCoursesByDomain] = useState<CoursesByDomain>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchCoursesByDomain = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:5001/api/courses/by-domain');
        if (!response.ok) {
          throw new Error('Failed to fetch courses');
        }
        const data = await response.json();
        setCoursesByDomain(data);
      } catch (err: unknown) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCoursesByDomain();
  }, []);

  const getDomainDescription = (domain: string) => {
    const descriptions: { [key: string]: string } = {
      'Healthcare & Medicine': 'Medical education, healthcare training, and clinical skills',
      'Insurance & Risk Management': 'Insurance policies, risk assessment, and compliance',
      'Science': 'Scientific research, laboratory skills, and theoretical knowledge',
      'Technology': 'Programming, software development, and technical skills',
      'Engineering': 'Engineering principles, design, and problem-solving',
      'Mathematics': 'Mathematical concepts, statistics, and analytical thinking',
      'Web Development': 'Frontend, backend, and full-stack development',
      'Data Science': 'Data analysis, machine learning, and statistical modeling',
      'Design': 'UI/UX design, graphic design, and creative skills',
      'Business': 'Business strategy, management, and entrepreneurship',
      'Finance': 'Financial analysis, investment, and economic principles'
    };
    return descriptions[domain] || 'Comprehensive courses in this field';
  };

  const filteredDomains = Object.keys(coursesByDomain).filter(domain => {
    if (!searchQuery) return true;
    const domainMatches = domain.toLowerCase().includes(searchQuery.toLowerCase());
    const coursesMatch = coursesByDomain[domain].some(course =>
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
    return domainMatches || coursesMatch;
  });

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-[#fafbfc]">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-gray-600">Loading courses...</div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-[#fafbfc]">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-red-600">Error: {error}</div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#fafbfc]">
      <Navbar />
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            All Courses by Industry
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Discover courses tailored to your professional field and interests
          </p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search courses..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            />
          </div>
        </div>

        {/* Course Domains */}
        {filteredDomains.length === 0 ? (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faSearch} className="text-gray-400 text-4xl mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No courses found</h3>
            <p className="text-gray-500">Try adjusting your search terms</p>
          </div>
        ) : (
          filteredDomains.map((domain) => {
            const courses = coursesByDomain[domain];
            const filteredCourses = searchQuery
              ? courses.filter(course =>
                  course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  course.description.toLowerCase().includes(searchQuery.toLowerCase())
                )
              : courses;

            if (filteredCourses.length === 0) return null;

            return (
              <div key={domain} className="mb-12">
                {/* Domain Header */}
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">{domain}</h2>
                    <p className="text-gray-600">{getDomainDescription(domain)}</p>
                  </div>
                  <div className="text-blue-500 font-medium flex items-center">
                    Total: {courses.length} course{courses.length !== 1 ? 's' : ''}
                  </div>
                </div>

                {/* Course Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredCourses.map((course) => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </div>
            );
          })
        )}

        {/* Statistics Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mt-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-500 mb-2">
                {Object.values(coursesByDomain).flat().length}
              </div>
              <div className="text-gray-600">Total Courses</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-500 mb-2">
                {Object.keys(coursesByDomain).length}
              </div>
              <div className="text-gray-600">Industry Domains</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-500 mb-2">
                {Object.values(coursesByDomain).flat().reduce((sum, course) => sum + course.estimated_hours, 0)}
              </div>
              <div className="text-gray-600">Learning Hours</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
