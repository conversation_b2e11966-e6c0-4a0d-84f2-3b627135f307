"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/discussion/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/courses/[id]/discussion/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscussionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_DiscussionForum__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/DiscussionForum */ \"(app-pages-browser)/./src/components/DiscussionForum.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DiscussionPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const courseId = params.id;\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        // Fallback to user data if token doesn't work\n        const userData = localStorage.getItem('user');\n        if (userData) {\n            try {\n                return JSON.parse(userData);\n            } catch (error) {\n                console.error('Error parsing user data:', error);\n            }\n        }\n        return null;\n    };\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch course details\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscussionPage.useEffect\": ()=>{\n            const fetchCourse = {\n                \"DiscussionPage.useEffect.fetchCourse\": async ()=>{\n                    try {\n                        const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (response.ok) {\n                            const courseData = await response.json();\n                            setCourse(courseData);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching course:', error);\n                    }\n                }\n            }[\"DiscussionPage.useEffect.fetchCourse\"];\n            if (courseId) {\n                fetchCourse();\n            }\n        }\n    }[\"DiscussionPage.useEffect\"], [\n        courseId\n    ]);\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscussionPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            if (user) {\n                setCurrentUser(user);\n            }\n            setLoading(false);\n        }\n    }[\"DiscussionPage.useEffect\"], []);\n    const handleBackToCourse = ()=>{\n        router.push(\"/courses/\".concat(courseId));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Loading discussion forum...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleBackToCourse,\n                                        className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Course\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    course && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-px bg-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Discussion Forum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                        children: course === null || course === void 0 ? void 0 : course.domain\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                        children: course === null || course === void 0 ? void 0 : course.level\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: userId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiscussionForum__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        courseId: courseId,\n                        userId: userId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Login Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-6\",\n                                    children: \"Please log in to access the discussion forum and connect with other learners.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push('/signin'),\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: \"Go to Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleBackToCourse,\n                                            className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: \"Back to Course\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\discussion\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscussionPage, \"W/mWipC78zh17Q3Nd3kRtYdPUDY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DiscussionPage;\nvar _c;\n$RefreshReg$(_c, \"DiscussionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/discussion/page.tsx\n"));

/***/ })

});