"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/courses/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CourseViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_DiscussionForum__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/DiscussionForum */ \"(app-pages-browser)/./src/components/DiscussionForum.tsx\");\n/* harmony import */ var _components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/QnAWithHeyGenModal */ \"(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\");\n/* harmony import */ var _components_AssessmentModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/AssessmentModal */ \"(app-pages-browser)/./src/components/AssessmentModal.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CourseViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams)();\n    const courseId = params.id;\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('Overview');\n    const [showAssessmentModal, setShowAssessmentModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [currentVideoIndex, setCurrentVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [leftSidebarVisible, setLeftSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [rightSidebarVisible, setRightSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [hasStartedCourse, setHasStartedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [courseProgress, setCourseProgress] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [moduleProgress, setModuleProgress] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [isQnAModalOpen, setIsQnAModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        return null;\n    };\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            if (user) {\n                setCurrentUser(user);\n            } else {\n                // Redirect to login if no valid token\n                window.location.href = '/signin';\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], []);\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch user progress\n    const fetchUserProgress = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[fetchUserProgress]\": async ()=>{\n            if (!userId) return; // Don't fetch if no user ID\n            try {\n                const progressResponse = await fetch(\"http://localhost:5001/api/users/\".concat(userId, \"/courses/\").concat(courseId, \"/progress\"));\n                if (progressResponse.ok) {\n                    const progressData = await progressResponse.json();\n                    setCourseProgress(progressData.courseProgress);\n                    setModuleProgress(progressData.moduleProgress);\n                    // Set current video index based on progress\n                    if (progressData.courseProgress && progressData.courseProgress.current_module > 1) {\n                        setCurrentVideoIndex(progressData.courseProgress.current_module - 1);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[fetchUserProgress]\"], [\n        userId,\n        courseId\n    ]);\n    // Function to start course (track course enrollment)\n    const startCourse = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[startCourse]\": async ()=>{\n            if (!userId) return; // Don't start if no user ID\n            try {\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/start\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId\n                    })\n                });\n                if (response.ok) {\n                    setHasStartedCourse(true);\n                    await fetchUserProgress(); // Refresh progress data\n                    console.log('Course started for course ID:', courseId);\n                }\n            } catch (error) {\n                console.error('Error starting course:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[startCourse]\"], [\n        userId,\n        courseId,\n        fetchUserProgress\n    ]);\n    // Function to update module progress\n    const updateModuleProgress = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)({\n        \"CourseViewPage.useCallback[updateModuleProgress]\": async function(moduleNumber, watchTime, totalDuration) {\n            let isCompleted = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n            if (!userId) return; // Don't update if no user ID\n            try {\n                const currentVideo = videos[moduleNumber - 1];\n                const requestData = {\n                    userId,\n                    watchTime,\n                    totalDuration,\n                    isCompleted,\n                    moduleTitle: (currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.page_title) || \"Module \".concat(moduleNumber)\n                };\n                console.log('Updating module progress:', requestData);\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/modules/\").concat(moduleNumber, \"/progress\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(requestData)\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log('Progress update successful:', result);\n                    await fetchUserProgress(); // Refresh progress data\n                } else {\n                    const errorData = await response.json();\n                    console.error('Progress update failed:', errorData);\n                }\n            } catch (error) {\n                console.error('Error updating module progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[updateModuleProgress]\"], [\n        userId,\n        courseId,\n        videos,\n        fetchUserProgress\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const fetchCourseData = {\n                \"CourseViewPage.useEffect.fetchCourseData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch course details\n                        const courseResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (!courseResponse.ok) {\n                            throw new Error('Failed to fetch course details');\n                        }\n                        const courseData = await courseResponse.json();\n                        setCourse(courseData);\n                        // Fetch course videos\n                        const videosResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/videos\"));\n                        if (!videosResponse.ok) {\n                            throw new Error('Failed to fetch course videos');\n                        }\n                        const videosData = await videosResponse.json();\n                        setVideos(videosData);\n                        // Fetch user progress for this course (only if user is authenticated)\n                        if (userId) {\n                            await fetchUserProgress();\n                        }\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.fetchCourseData\"];\n            if (courseId && userId) {\n                fetchCourseData();\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        courseId,\n        userId\n    ]);\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"CourseViewPage.useEffect.handleKeyPress\": (event)=>{\n                    if (videos.length === 0) return;\n                    if (event.key === 'ArrowLeft' && currentVideoIndex > 0) {\n                        setCurrentVideoIndex(currentVideoIndex - 1);\n                    } else if (event.key === 'ArrowRight' && currentVideoIndex < videos.length - 1) {\n                        setCurrentVideoIndex(currentVideoIndex + 1);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"CourseViewPage.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"CourseViewPage.useEffect\"];\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        currentVideoIndex,\n        videos.length\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading course...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !course) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-red-600\",\n                            children: error || 'Course not found'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    const currentVideo = videos[currentVideoIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-64px)] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(leftSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-gray-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800\",\n                                                    children: \"Course Curriculum\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        videos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                videos.length,\n                                                \" modules\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto\",\n                                    children: videos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            videos.map((video, index)=>{\n                                                const moduleProgressData = moduleProgress.find((mp)=>mp.module_number === video.page_number);\n                                                const isCompleted = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.is_completed) || false;\n                                                const completionPercentage = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.completion_percentage) || 0;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(index === currentVideoIndex ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50', \" \").concat(isCompleted ? 'bg-green-50' : ''),\n                                                    onClick: ()=>{\n                                                        setCurrentVideoIndex(index);\n                                                        if (!hasStartedCourse) {\n                                                            startCourse();\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold \".concat(isCompleted ? 'bg-green-500 text-white' : index === currentVideoIndex ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'),\n                                                                children: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 31\n                                                                }, this) : index === currentVideoIndex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 31\n                                                                }, this) : video.page_number\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-sm leading-tight \".concat(isCompleted ? 'text-green-800' : index === currentVideoIndex ? 'text-blue-800' : 'text-gray-800'),\n                                                                        children: [\n                                                                            \"Module \",\n                                                                            video.page_number,\n                                                                            \": \",\n                                                                            video.page_title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs mt-1 \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                        children: [\n                                                                            video.avatar_name,\n                                                                            \" • 30 min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                                children: isCompleted ? 'Completed' : completionPercentage > 0 ? \"\".concat(Math.round(completionPercentage), \"% watched\") : 'Not started'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            completionPercentage > 0 && !isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 bg-gray-200 rounded-full h-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-blue-500 h-1 rounded-full transition-all duration-300\",\n                                                                                    style: {\n                                                                                        width: \"\".concat(completionPercentage, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 339,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, video.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 23\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 rounded-lg border cursor-pointer transition-all \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-50 border-green-200 hover:bg-green-100' : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'),\n                                                onClick: ()=>{\n                                                    if ((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100) {\n                                                        setShowAssessmentModal(true);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-100' : 'bg-gray-100'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-sm \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-800' : 'text-gray-500'),\n                                                                    children: \"Take Final Assessment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                    children: (courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'Click to start assessment' : 'Complete all modules to unlock assessment'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 text-center text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No curriculum available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setLeftSidebarVisible(!leftSidebarVisible),\n                            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                left: leftSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(leftSidebarVisible ? 'rotate-180' : ''),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black relative\",\n                                    children: [\n                                        videos.length > 0 && currentVideo && currentVideo.heygenbloburl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"w-full h-[400px] object-cover\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            onPlay: ()=>{\n                                                if (!hasStartedCourse) {\n                                                    startCourse();\n                                                }\n                                            },\n                                            onTimeUpdate: (e)=>{\n                                                const video = e.target;\n                                                const watchTime = Math.floor(video.currentTime);\n                                                const totalDuration = Math.floor(video.duration);\n                                                // Update progress every 10 seconds\n                                                if (watchTime % 10 === 0 && watchTime > 0) {\n                                                    updateModuleProgress(currentVideo.page_number, watchTime, totalDuration);\n                                                }\n                                            },\n                                            onEnded: ()=>{\n                                                const video = document.querySelector('video');\n                                                if (video) {\n                                                    const totalDuration = Math.floor(video.duration);\n                                                    updateModuleProgress(currentVideo.page_number, totalDuration, totalDuration, true);\n                                                }\n                                                // Auto-advance to next video after a short delay\n                                                setTimeout(()=>{\n                                                    if (currentVideoIndex < videos.length - 1) {\n                                                        setCurrentVideoIndex(currentVideoIndex + 1);\n                                                    }\n                                                }, 1500); // 1.5 second delay before auto-advancing\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                    src: currentVideo.heygenbloburl,\n                                                    type: \"video/mp4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Your browser does not support the video tag.\"\n                                            ]\n                                        }, currentVideo.heygenbloburl, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: videos.length === 0 ? 'No videos available for this course' : 'Loading video...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsQnAModalOpen(true),\n                                            className: \"absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10\",\n                                            title: \"Raise Hand\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 group-hover:animate-bounce\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 px-6\",\n                                        children: [\n                                            'Overview',\n                                            'Assessment',\n                                            'Discussion',\n                                            'Reviews'\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"py-4 font-medium border-b-2 transition-colors \".concat(activeTab === tab && tab !== 'Assessment' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                                onClick: ()=>{\n                                                    if (tab === 'Assessment') {\n                                                        setShowAssessmentModal(true);\n                                                    } else {\n                                                        setActiveTab(tab);\n                                                    }\n                                                },\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 bg-white p-6 overflow-y-auto\",\n                                    children: [\n                                        activeTab === 'Overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"About This Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-6\",\n                                                    children: \"Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"What you'll learn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2 text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Industry best practices and standards\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Practical implementation strategies\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Real-world case studies and examples\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Professional certification preparation\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Prerequisites\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700 mb-4\",\n                                                                    children: \"Advanced knowledge and experience required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Course Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Domain: \",\n                                                                                course.domain\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Level: \",\n                                                                                course.level\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Duration: \",\n                                                                                course.estimated_hours,\n                                                                                \" hours\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Modules: \",\n                                                                                videos.length\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Certificate: Available upon completion\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        activeTab === 'Discussion' && (userId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiscussionForum__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            courseId: courseId,\n                                            userId: userId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Please log in to access the discussion forum.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this)),\n                                        activeTab === 'Reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-8\",\n                                            children: \"Student reviews coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRightSidebarVisible(!rightSidebarVisible),\n                            className: \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                right: rightSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(rightSidebarVisible ? '' : 'rotate-180'),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(rightSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-2\",\n                                            children: \"Course Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-3\",\n                                            children: course.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium\",\n                                                children: course.domain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-yellow-400\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"4.8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"(234 reviews)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                course.estimated_hours,\n                                                                \" hours\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                videos.length,\n                                                                \" students\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: course.level\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Certificate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this),\n                                videos.length > 0 && courseProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-3\",\n                                            children: \"Course Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Overall Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold text-orange-600\",\n                                                            children: [\n                                                                Math.round(courseProgress.progress_percentage),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2 mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(courseProgress.progress_percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                courseProgress.completed_modules,\n                                                                \" of \",\n                                                                courseProgress.total_modules,\n                                                                \" modules completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Status: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(courseProgress.status === 'completed' ? 'text-green-600' : courseProgress.status === 'in_progress' ? 'text-blue-600' : 'text-gray-600'),\n                                                                    children: courseProgress.status === 'completed' ? 'Completed' : courseProgress.status === 'in_progress' ? 'In Progress' : 'Not Started'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        courseProgress.started_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(courseProgress.started_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        courseProgress.completed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Completed: \",\n                                                                new Date(courseProgress.completed_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-blue-800 mb-1\",\n                                                    children: \"Currently Watching\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Module \",\n                                                        currentVideo.page_number,\n                                                        \": \",\n                                                        currentVideo.page_title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: currentVideo.avatar_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isQnAModalOpen,\n                onClose: ()=>setIsQnAModalOpen(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AssessmentModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAssessmentModal,\n                onClose: ()=>setShowAssessmentModal(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 725,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseViewPage, \"9W4eYzNQzfJ2k4tq3cxwy7MIb2A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useParams\n    ];\n});\n_c = CourseViewPage;\nvar _c;\n$RefreshReg$(_c, \"CourseViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/page.tsx\n"));

/***/ })

});