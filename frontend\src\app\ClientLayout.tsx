"use client";
import React from "react";
import Sidebar from "@/components/Layouts/SideNav/SideBar";
import { NotificationProvider } from "../contexts/NotificationContext";
import Header from "@/components/Layouts/Header/page";
import { usePathname } from "next/navigation";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarExpanded, setSidebarExpanded] = React.useState(true);
  const pathname = usePathname();
  const hideLayoutPaths = [
    "/",
    "/courses", "/courses/",
    "/signin", "/signin/",
    "/signup", "/signup/",
    "/forgot-password", "/forgot-password/",
    "/my-dashboard", "/my-dashboard/",
    "/my-learning", "/my-learning/",
    "/my-certificates", "/my-certificates/"
  ];

  // Check for exact matches or dynamic course routes (including discussion pages)
  const hideLayout = hideLayoutPaths.includes(pathname) ||
    pathname.startsWith("/courses/") && pathname.match(/^\/courses\/\d+(\/.*)?$/);
  if (hideLayout) {
    return <NotificationProvider>{children}</NotificationProvider>;
  }
  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <Sidebar sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded} />
      <div className="flex flex-col flex-1 min-h-screen">
        <NotificationProvider>
          {/* Header */}
          <Header sidebarExpanded={sidebarExpanded}/>
          {/* Main Content */}
          <main className="flex-1">
            {children}
          </main>
        </NotificationProvider>
      </div>
    </div>
  );
}
