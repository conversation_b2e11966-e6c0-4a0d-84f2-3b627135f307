'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faComment,
  faUser,
  faClock,
  faReply,
  faPlus,
  faPaperPlane,
  faComments,
  faUserCircle,
  faCalendarAlt,
  faTimes,
  faSearch,
  faThumbsUp,
  faEye,
  faFire,
  faUsers
} from '@fortawesome/free-solid-svg-icons';

interface Discussion {
  id: number;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  username: string;
  reply_count: number;
}

interface Reply {
  id: number;
  content: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  username: string;
}

interface DiscussionForumProps {
  courseId: string;
  userId: number | undefined;
}

export default function DiscussionForum({ courseId, userId }: DiscussionForumProps) {
  const [discussions, setDiscussions] = useState<Discussion[]>([]);
  const [selectedDiscussion, setSelectedDiscussion] = useState<Discussion | null>(null);
  const [replies, setReplies] = useState<Reply[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewDiscussionForm, setShowNewDiscussionForm] = useState(false);
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form states
  const [newDiscussionTitle, setNewDiscussionTitle] = useState('');
  const [newDiscussionContent, setNewDiscussionContent] = useState('');
  const [newReplyContent, setNewReplyContent] = useState('');

  // Fetch discussions for the course
  const fetchDiscussions = useCallback(async () => {
    try {
      const response = await fetch(`http://localhost:5001/api/courses/${courseId}/discussions`);
      if (response.ok) {
        const data = await response.json();
        setDiscussions(data);
      }
    } catch (error) {
      console.error('Error fetching discussions:', error);
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  // Fetch replies for a specific discussion
  const fetchReplies = useCallback(async (discussionId: number) => {
    try {
      const response = await fetch(`http://localhost:5001/api/discussions/${discussionId}/replies`);
      if (response.ok) {
        const data = await response.json();
        setReplies(data);
      }
    } catch (error) {
      console.error('Error fetching replies:', error);
    }
  }, []);

  // Create new discussion
  const createDiscussion = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newDiscussionTitle.trim() || !newDiscussionContent.trim() || !userId) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`http://localhost:5001/api/courses/${courseId}/discussions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          title: newDiscussionTitle,
          content: newDiscussionContent,
        }),
      });

      if (response.ok) {
        const newDiscussion = await response.json();
        setDiscussions([newDiscussion, ...discussions]);
        setNewDiscussionTitle('');
        setNewDiscussionContent('');
        setShowNewDiscussionForm(false);
      }
    } catch (error) {
      console.error('Error creating discussion:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create new reply
  const createReply = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newReplyContent.trim() || !selectedDiscussion || !userId) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`http://localhost:5001/api/discussions/${selectedDiscussion.id}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          content: newReplyContent,
        }),
      });

      if (response.ok) {
        const newReply = await response.json();
        setReplies([...replies, newReply]);
        setNewReplyContent('');
        setShowReplyForm(false);

        // Update reply count in discussions list
        setDiscussions(discussions.map(d =>
          d.id === selectedDiscussion.id
            ? { ...d, reply_count: d.reply_count + 1 }
            : d
        ));
      }
    } catch (error) {
      console.error('Error creating reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date with relative time
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`;

    return date.toLocaleDateString();
  };

  // Get user avatar initials
  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Filter discussions based on search
  const filteredDiscussions = discussions.filter(discussion =>
    discussion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    discussion.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    `${discussion.first_name} ${discussion.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle discussion selection
  const selectDiscussion = (discussion: Discussion) => {
    setSelectedDiscussion(discussion);
    setShowReplyForm(false);
    fetchReplies(discussion.id);
  };

  useEffect(() => {
    if (courseId && userId) {
      fetchDiscussions();
    }
  }, [courseId, userId, fetchDiscussions]);

  if (!userId) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="flex flex-col items-center">
          <div className="text-gray-600 font-medium">Please log in to access discussions</div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <div className="text-gray-600 font-medium">Loading discussions...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-white rounded-lg shadow-sm">
      {/* Modern Header with Stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-gray-200 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl">
              <FontAwesomeIcon icon={faComments} className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Discussion Forum</h2>
              <p className="text-gray-600">Connect with other learners, ask questions, and share insights</p>
            </div>
          </div>
          <button
            onClick={() => setShowNewDiscussionForm(true)}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <FontAwesomeIcon icon={faPlus} />
            Ask a Question
          </button>
        </div>

        {/* Stats Bar */}
        <div className="flex items-center gap-6 pt-4 border-t border-gray-100">
          <div className="flex items-center gap-2 text-gray-600">
            <FontAwesomeIcon icon={faComments} className="text-blue-500" />
            <span className="font-medium">{discussions.length}</span>
            <span>Discussions</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600">
            <FontAwesomeIcon icon={faUsers} className="text-green-500" />
            <span className="font-medium">{new Set(discussions.map(d => d.username)).size}</span>
            <span>Active Members</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600">
            <FontAwesomeIcon icon={faFire} className="text-orange-500" />
            <span className="font-medium">{discussions.reduce((sum, d) => sum + d.reply_count, 0)}</span>
            <span>Total Replies</span>
          </div>
        </div>
      </div>

      {/* Enhanced New Discussion Form */}
      {showNewDiscussionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg">
                  <FontAwesomeIcon icon={faComment} className="text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800">Ask a Question</h3>
              </div>
              <button
                onClick={() => setShowNewDiscussionForm(false)}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>

            <form onSubmit={createDiscussion} className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Question Title *
                </label>
                <input
                  type="text"
                  value={newDiscussionTitle}
                  onChange={(e) => setNewDiscussionTitle(e.target.value)}
                  placeholder="What's your question about?"
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Question Details *
                </label>
                <textarea
                  value={newDiscussionContent}
                  onChange={(e) => setNewDiscussionContent(e.target.value)}
                  placeholder="Provide more context about your question. The more details you share, the better answers you'll get!"
                  rows={6}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 resize-none"
                  required
                />
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Posting...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faPaperPlane} />
                      Post Question
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowNewDiscussionForm(false)}
                  className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Main Content with Search */}
      <div className="flex-1 flex gap-6">
        {/* Enhanced Discussions List */}
        <div className="w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
            <div className="flex items-center justify-between">
              <h3 className="font-bold text-white text-lg">All Discussions</h3>
              <div className="text-white text-sm">
                {filteredDiscussions.length} of {discussions.length}
              </div>
            </div>

            {/* Search Bar */}
            <div className="mt-4 relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-200" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search discussions..."
                className="w-full pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-blue-300 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:bg-opacity-30 focus:border-blue-100"
              />
            </div>
          </div>

          <div className="overflow-y-auto max-h-[600px]">
            {filteredDiscussions.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                {searchTerm ? (
                  <>
                    <FontAwesomeIcon icon={faSearch} className="text-4xl mb-3 text-gray-300" />
                    <p>No discussions found matching "{searchTerm}"</p>
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faComments} className="text-4xl mb-3 text-gray-300" />
                    <p>No discussions yet. Be the first to ask a question!</p>
                  </>
                )}
              </div>
            ) : (
              filteredDiscussions.map((discussion) => (
                <div
                  key={discussion.id}
                  onClick={() => selectDiscussion(discussion)}
                  className={`p-5 border-b border-gray-100 cursor-pointer transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 ${
                    selectedDiscussion?.id === discussion.id
                      ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500 shadow-inner'
                      : ''
                  }`}
                >
                  <div className="flex items-start gap-4">
                    {/* User Avatar */}
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0">
                      {getUserInitials(discussion.first_name, discussion.last_name)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-800 mb-2 line-clamp-2">{discussion.title}</h4>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{discussion.content}</p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span className="font-medium">{discussion.first_name} {discussion.last_name}</span>
                          <span>•</span>
                          <span>{formatDate(discussion.created_at)}</span>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                            <FontAwesomeIcon icon={faReply} />
                            <span>{discussion.reply_count}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                            <FontAwesomeIcon icon={faEye} />
                            <span>{Math.floor(Math.random() * 50) + 10}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Enhanced Discussion Detail */}
        <div className="w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
          {selectedDiscussion ? (
            <>
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-bold text-white text-lg">Discussion Details</h3>
                  <div className="flex items-center gap-2 text-indigo-200 text-sm">
                    <FontAwesomeIcon icon={faReply} />
                    <span>{replies.length} replies</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col h-[600px]">
                <div className="flex-1 overflow-y-auto p-6">
                  {/* Original Discussion with Enhanced Design */}
                  <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border-l-4 border-blue-500">
                    <div className="flex items-start gap-4 mb-4">
                      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0">
                        {getUserInitials(selectedDiscussion.first_name, selectedDiscussion.last_name)}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-gray-800 text-lg mb-2">{selectedDiscussion.title}</h4>
                        <div className="flex items-center gap-3 text-sm text-gray-600 mb-3">
                          <span className="font-semibold">{selectedDiscussion.first_name} {selectedDiscussion.last_name}</span>
                          <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs">Original Poster</span>
                          <span>•</span>
                          <div className="flex items-center gap-1">
                            <FontAwesomeIcon icon={faCalendarAlt} />
                            <span>{formatDate(selectedDiscussion.created_at)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-700 leading-relaxed">{selectedDiscussion.content}</p>
                  </div>

                  {/* Replies with Enhanced Design */}
                  {replies.length > 0 && (
                    <div className="space-y-4">
                      <h5 className="font-semibold text-gray-800 flex items-center gap-2">
                        <FontAwesomeIcon icon={faComments} className="text-indigo-500" />
                        Replies ({replies.length})
                      </h5>
                      {replies.map((reply, index) => (
                        <div key={reply.id} className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-200">
                          <div className="flex items-start gap-3">
                            <div className="bg-gradient-to-r from-gray-500 to-gray-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0">
                              {getUserInitials(reply.first_name, reply.last_name)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <span className="font-semibold text-gray-800">{reply.first_name} {reply.last_name}</span>
                                <span className="text-gray-400">•</span>
                                <span className="text-sm text-gray-500">{formatDate(reply.created_at)}</span>
                                <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">#{index + 1}</span>
                              </div>
                              <p className="text-gray-700 leading-relaxed">{reply.content}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Enhanced Reply Form */}
                <div className="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6">
                  {showReplyForm ? (
                    <form onSubmit={createReply} className="space-y-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold text-sm">
                          <FontAwesomeIcon icon={faReply} />
                        </div>
                        <span className="font-semibold text-gray-700">Add your reply</span>
                      </div>
                      <textarea
                        value={newReplyContent}
                        onChange={(e) => setNewReplyContent(e.target.value)}
                        placeholder="Share your thoughts, provide an answer, or ask for clarification..."
                        rows={4}
                        className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 resize-none"
                        required
                      />
                      <div className="flex gap-3">
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-4 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              Posting...
                            </>
                          ) : (
                            <>
                              <FontAwesomeIcon icon={faPaperPlane} />
                              Post Reply
                            </>
                          )}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowReplyForm(false)}
                          className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200"
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  ) : (
                    <button
                      onClick={() => setShowReplyForm(true)}
                      className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white py-4 rounded-xl flex items-center justify-center gap-3 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      <FontAwesomeIcon icon={faReply} className="text-lg" />
                      Join the Discussion
                    </button>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-[600px] text-gray-500">
              <div className="text-center p-8">
                <div className="bg-gradient-to-r from-gray-200 to-gray-300 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FontAwesomeIcon icon={faComments} className="text-3xl text-gray-400" />
                </div>
                <h4 className="text-lg font-semibold text-gray-600 mb-2">Select a Discussion</h4>
                <p className="text-gray-500">Choose a discussion from the left to view details and join the conversation</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
