"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/courses/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CourseViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/QnAWithHeyGenModal */ \"(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\");\n/* harmony import */ var _components_AssessmentModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/AssessmentModal */ \"(app-pages-browser)/./src/components/AssessmentModal.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CourseViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const courseId = params.id;\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('Overview');\n    const [showAssessmentModal, setShowAssessmentModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentVideoIndex, setCurrentVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [leftSidebarVisible, setLeftSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [rightSidebarVisible, setRightSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [hasStartedCourse, setHasStartedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [courseProgress, setCourseProgress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [moduleProgress, setModuleProgress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [isQnAModalOpen, setIsQnAModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        return null;\n    };\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            if (user) {\n                setCurrentUser(user);\n            } else {\n                // Redirect to login if no valid token\n                window.location.href = '/signin';\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], []);\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch user progress\n    const fetchUserProgress = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[fetchUserProgress]\": async ()=>{\n            if (!userId) return; // Don't fetch if no user ID\n            try {\n                const progressResponse = await fetch(\"http://localhost:5001/api/users/\".concat(userId, \"/courses/\").concat(courseId, \"/progress\"));\n                if (progressResponse.ok) {\n                    const progressData = await progressResponse.json();\n                    setCourseProgress(progressData.courseProgress);\n                    setModuleProgress(progressData.moduleProgress);\n                    // Set current video index based on progress\n                    if (progressData.courseProgress && progressData.courseProgress.current_module > 1) {\n                        setCurrentVideoIndex(progressData.courseProgress.current_module - 1);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[fetchUserProgress]\"], [\n        userId,\n        courseId\n    ]);\n    // Function to start course (track course enrollment)\n    const startCourse = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[startCourse]\": async ()=>{\n            if (!userId) return; // Don't start if no user ID\n            try {\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/start\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId\n                    })\n                });\n                if (response.ok) {\n                    setHasStartedCourse(true);\n                    await fetchUserProgress(); // Refresh progress data\n                    console.log('Course started for course ID:', courseId);\n                }\n            } catch (error) {\n                console.error('Error starting course:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[startCourse]\"], [\n        userId,\n        courseId,\n        fetchUserProgress\n    ]);\n    // Function to update module progress\n    const updateModuleProgress = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[updateModuleProgress]\": async function(moduleNumber, watchTime, totalDuration) {\n            let isCompleted = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n            if (!userId) return; // Don't update if no user ID\n            try {\n                const currentVideo = videos[moduleNumber - 1];\n                const requestData = {\n                    userId,\n                    watchTime,\n                    totalDuration,\n                    isCompleted,\n                    moduleTitle: (currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.page_title) || \"Module \".concat(moduleNumber)\n                };\n                console.log('Updating module progress:', requestData);\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/modules/\").concat(moduleNumber, \"/progress\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(requestData)\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log('Progress update successful:', result);\n                    await fetchUserProgress(); // Refresh progress data\n                } else {\n                    const errorData = await response.json();\n                    console.error('Progress update failed:', errorData);\n                }\n            } catch (error) {\n                console.error('Error updating module progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[updateModuleProgress]\"], [\n        userId,\n        courseId,\n        videos,\n        fetchUserProgress\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const fetchCourseData = {\n                \"CourseViewPage.useEffect.fetchCourseData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch course details\n                        const courseResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (!courseResponse.ok) {\n                            throw new Error('Failed to fetch course details');\n                        }\n                        const courseData = await courseResponse.json();\n                        setCourse(courseData);\n                        // Fetch course videos\n                        const videosResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/videos\"));\n                        if (!videosResponse.ok) {\n                            throw new Error('Failed to fetch course videos');\n                        }\n                        const videosData = await videosResponse.json();\n                        setVideos(videosData);\n                        // Fetch user progress for this course (only if user is authenticated)\n                        if (userId) {\n                            await fetchUserProgress();\n                        }\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.fetchCourseData\"];\n            if (courseId && userId) {\n                fetchCourseData();\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        courseId,\n        userId\n    ]);\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"CourseViewPage.useEffect.handleKeyPress\": (event)=>{\n                    if (videos.length === 0) return;\n                    if (event.key === 'ArrowLeft' && currentVideoIndex > 0) {\n                        setCurrentVideoIndex(currentVideoIndex - 1);\n                    } else if (event.key === 'ArrowRight' && currentVideoIndex < videos.length - 1) {\n                        setCurrentVideoIndex(currentVideoIndex + 1);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"CourseViewPage.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"CourseViewPage.useEffect\"];\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        currentVideoIndex,\n        videos.length\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading course...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !course) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-red-600\",\n                            children: error || 'Course not found'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    const currentVideo = videos[currentVideoIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-64px)] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(leftSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-gray-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800\",\n                                                    children: \"Course Curriculum\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        videos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                videos.length,\n                                                \" modules\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto\",\n                                    children: videos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            videos.map((video, index)=>{\n                                                const moduleProgressData = moduleProgress.find((mp)=>mp.module_number === video.page_number);\n                                                const isCompleted = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.is_completed) || false;\n                                                const completionPercentage = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.completion_percentage) || 0;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(index === currentVideoIndex ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50', \" \").concat(isCompleted ? 'bg-green-50' : ''),\n                                                    onClick: ()=>{\n                                                        setCurrentVideoIndex(index);\n                                                        if (!hasStartedCourse) {\n                                                            startCourse();\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold \".concat(isCompleted ? 'bg-green-500 text-white' : index === currentVideoIndex ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'),\n                                                                children: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 31\n                                                                }, this) : index === currentVideoIndex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 31\n                                                                }, this) : video.page_number\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-sm leading-tight \".concat(isCompleted ? 'text-green-800' : index === currentVideoIndex ? 'text-blue-800' : 'text-gray-800'),\n                                                                        children: [\n                                                                            \"Module \",\n                                                                            video.page_number,\n                                                                            \": \",\n                                                                            video.page_title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs mt-1 \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                        children: [\n                                                                            video.avatar_name,\n                                                                            \" • 30 min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                                children: isCompleted ? 'Completed' : completionPercentage > 0 ? \"\".concat(Math.round(completionPercentage), \"% watched\") : 'Not started'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            completionPercentage > 0 && !isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 bg-gray-200 rounded-full h-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-blue-500 h-1 rounded-full transition-all duration-300\",\n                                                                                    style: {\n                                                                                        width: \"\".concat(completionPercentage, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 339,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, video.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 23\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 rounded-lg border cursor-pointer transition-all \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-50 border-green-200 hover:bg-green-100' : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'),\n                                                onClick: ()=>{\n                                                    if ((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100) {\n                                                        setShowAssessmentModal(true);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-100' : 'bg-gray-100'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-sm \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-800' : 'text-gray-500'),\n                                                                    children: \"Take Final Assessment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                    children: (courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'Click to start assessment' : 'Complete all modules to unlock assessment'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 text-center text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No curriculum available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setLeftSidebarVisible(!leftSidebarVisible),\n                            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                left: leftSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(leftSidebarVisible ? 'rotate-180' : ''),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black relative\",\n                                    children: [\n                                        videos.length > 0 && currentVideo && currentVideo.heygenbloburl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"w-full h-[400px] object-cover\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            onPlay: ()=>{\n                                                if (!hasStartedCourse) {\n                                                    startCourse();\n                                                }\n                                            },\n                                            onTimeUpdate: (e)=>{\n                                                const video = e.target;\n                                                const watchTime = Math.floor(video.currentTime);\n                                                const totalDuration = Math.floor(video.duration);\n                                                // Update progress every 10 seconds\n                                                if (watchTime % 10 === 0 && watchTime > 0) {\n                                                    updateModuleProgress(currentVideo.page_number, watchTime, totalDuration);\n                                                }\n                                            },\n                                            onEnded: ()=>{\n                                                const video = document.querySelector('video');\n                                                if (video) {\n                                                    const totalDuration = Math.floor(video.duration);\n                                                    updateModuleProgress(currentVideo.page_number, totalDuration, totalDuration, true);\n                                                }\n                                                // Auto-advance to next video after a short delay\n                                                setTimeout(()=>{\n                                                    if (currentVideoIndex < videos.length - 1) {\n                                                        setCurrentVideoIndex(currentVideoIndex + 1);\n                                                    }\n                                                }, 1500); // 1.5 second delay before auto-advancing\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                    src: currentVideo.heygenbloburl,\n                                                    type: \"video/mp4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Your browser does not support the video tag.\"\n                                            ]\n                                        }, currentVideo.heygenbloburl, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: videos.length === 0 ? 'No videos available for this course' : 'Loading video...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsQnAModalOpen(true),\n                                            className: \"absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10\",\n                                            title: \"Raise Hand\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 group-hover:animate-bounce\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 px-6\",\n                                        children: [\n                                            'Overview',\n                                            'Assessment',\n                                            'Discussion',\n                                            'Reviews'\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"py-4 font-medium border-b-2 transition-colors \".concat(activeTab === tab && tab !== 'Assessment' && tab !== 'Discussion' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                                onClick: ()=>{\n                                                    if (tab === 'Assessment') {\n                                                        setShowAssessmentModal(true);\n                                                    } else if (tab === 'Discussion') {\n                                                        router.push(\"/courses/\".concat(courseId, \"/discussion\"));\n                                                    } else {\n                                                        setActiveTab(tab);\n                                                    }\n                                                },\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 bg-white p-6 overflow-y-auto\",\n                                    children: [\n                                        activeTab === 'Overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"About This Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-6\",\n                                                    children: \"Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"What you'll learn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2 text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 529,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Industry best practices and standards\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Practical implementation strategies\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 537,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Real-world case studies and examples\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Professional certification preparation\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Prerequisites\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700 mb-4\",\n                                                                    children: \"Advanced knowledge and experience required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Course Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Domain: \",\n                                                                                course.domain\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Level: \",\n                                                                                course.level\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Duration: \",\n                                                                                course.estimated_hours,\n                                                                                \" hours\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Modules: \",\n                                                                                videos.length\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Certificate: Available upon completion\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        activeTab === 'Reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-8\",\n                                            children: \"Student reviews coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRightSidebarVisible(!rightSidebarVisible),\n                            className: \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                right: rightSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(rightSidebarVisible ? '' : 'rotate-180'),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(rightSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-2\",\n                                            children: \"Course Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-3\",\n                                            children: course.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium\",\n                                                children: course.domain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-yellow-400\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"(234 reviews)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                course.estimated_hours,\n                                                                \" hours\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                videos.length,\n                                                                \" students\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: course.level\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Certificate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this),\n                                videos.length > 0 && courseProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-3\",\n                                            children: \"Course Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Overall Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold text-orange-600\",\n                                                            children: [\n                                                                Math.round(courseProgress.progress_percentage),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2 mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(courseProgress.progress_percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                courseProgress.completed_modules,\n                                                                \" of \",\n                                                                courseProgress.total_modules,\n                                                                \" modules completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Status: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(courseProgress.status === 'completed' ? 'text-green-600' : courseProgress.status === 'in_progress' ? 'text-blue-600' : 'text-gray-600'),\n                                                                    children: courseProgress.status === 'completed' ? 'Completed' : courseProgress.status === 'in_progress' ? 'In Progress' : 'Not Started'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        courseProgress.started_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(courseProgress.started_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        courseProgress.completed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Completed: \",\n                                                                new Date(courseProgress.completed_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-blue-800 mb-1\",\n                                                    children: \"Currently Watching\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Module \",\n                                                        currentVideo.page_number,\n                                                        \": \",\n                                                        currentVideo.page_title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: currentVideo.avatar_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: isQnAModalOpen,\n                onClose: ()=>setIsQnAModalOpen(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 712,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AssessmentModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAssessmentModal,\n                onClose: ()=>setShowAssessmentModal(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 719,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseViewPage, \"RVk41ZQYMnmK3mH1gQElnMAZbYc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = CourseViewPage;\nvar _c;\n$RefreshReg$(_c, \"CourseViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/page.tsx\n"));

/***/ })

});