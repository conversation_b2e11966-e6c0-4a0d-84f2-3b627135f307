"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/app/courses/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/courses/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CourseViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/QnAWithHeyGenModal */ \"(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\");\n/* harmony import */ var _components_AssessmentModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/AssessmentModal */ \"(app-pages-browser)/./src/components/AssessmentModal.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CourseViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const courseId = params.id;\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('Overview');\n    const [showAssessmentModal, setShowAssessmentModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [course, setCourse] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentVideoIndex, setCurrentVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [leftSidebarVisible, setLeftSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [rightSidebarVisible, setRightSidebarVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [hasStartedCourse, setHasStartedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [courseProgress, setCourseProgress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [moduleProgress, setModuleProgress] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [isQnAModalOpen, setIsQnAModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    // Get real user ID from authentication token\n    const getUserFromToken = ()=>{\n        const token = localStorage.getItem('token');\n        if (token) {\n            try {\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                return JSON.parse(jsonPayload);\n            } catch (error) {\n                console.error('Error decoding token:', error);\n                return null;\n            }\n        }\n        return null;\n    };\n    // Initialize user from token\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const user = getUserFromToken();\n            if (user) {\n                setCurrentUser(user);\n            } else {\n                // Redirect to login if no valid token\n                window.location.href = '/signin';\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], []);\n    const userId = currentUser === null || currentUser === void 0 ? void 0 : currentUser.id;\n    // Fetch user progress\n    const fetchUserProgress = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[fetchUserProgress]\": async ()=>{\n            if (!userId) return; // Don't fetch if no user ID\n            try {\n                const progressResponse = await fetch(\"http://localhost:5001/api/users/\".concat(userId, \"/courses/\").concat(courseId, \"/progress\"));\n                if (progressResponse.ok) {\n                    const progressData = await progressResponse.json();\n                    setCourseProgress(progressData.courseProgress);\n                    setModuleProgress(progressData.moduleProgress);\n                    // Set current video index based on progress\n                    if (progressData.courseProgress && progressData.courseProgress.current_module > 1) {\n                        setCurrentVideoIndex(progressData.courseProgress.current_module - 1);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[fetchUserProgress]\"], [\n        userId,\n        courseId\n    ]);\n    // Function to start course (track course enrollment)\n    const startCourse = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[startCourse]\": async ()=>{\n            if (!userId) return; // Don't start if no user ID\n            try {\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/start\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId\n                    })\n                });\n                if (response.ok) {\n                    setHasStartedCourse(true);\n                    await fetchUserProgress(); // Refresh progress data\n                    console.log('Course started for course ID:', courseId);\n                }\n            } catch (error) {\n                console.error('Error starting course:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[startCourse]\"], [\n        userId,\n        courseId,\n        fetchUserProgress\n    ]);\n    // Function to update module progress\n    const updateModuleProgress = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"CourseViewPage.useCallback[updateModuleProgress]\": async function(moduleNumber, watchTime, totalDuration) {\n            let isCompleted = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n            if (!userId) return; // Don't update if no user ID\n            try {\n                const currentVideo = videos[moduleNumber - 1];\n                const requestData = {\n                    userId,\n                    watchTime,\n                    totalDuration,\n                    isCompleted,\n                    moduleTitle: (currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.page_title) || \"Module \".concat(moduleNumber)\n                };\n                console.log('Updating module progress:', requestData);\n                const response = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/modules/\").concat(moduleNumber, \"/progress\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(requestData)\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log('Progress update successful:', result);\n                    await fetchUserProgress(); // Refresh progress data\n                } else {\n                    const errorData = await response.json();\n                    console.error('Progress update failed:', errorData);\n                }\n            } catch (error) {\n                console.error('Error updating module progress:', error);\n            }\n        }\n    }[\"CourseViewPage.useCallback[updateModuleProgress]\"], [\n        userId,\n        courseId,\n        videos,\n        fetchUserProgress\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const fetchCourseData = {\n                \"CourseViewPage.useEffect.fetchCourseData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch course details\n                        const courseResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId));\n                        if (!courseResponse.ok) {\n                            throw new Error('Failed to fetch course details');\n                        }\n                        const courseData = await courseResponse.json();\n                        setCourse(courseData);\n                        // Fetch course videos\n                        const videosResponse = await fetch(\"http://localhost:5001/api/courses/\".concat(courseId, \"/videos\"));\n                        if (!videosResponse.ok) {\n                            throw new Error('Failed to fetch course videos');\n                        }\n                        const videosData = await videosResponse.json();\n                        setVideos(videosData);\n                        // Fetch user progress for this course (only if user is authenticated)\n                        if (userId) {\n                            await fetchUserProgress();\n                        }\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'An error occurred');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.fetchCourseData\"];\n            if (courseId && userId) {\n                fetchCourseData();\n            }\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        courseId,\n        userId\n    ]);\n    // Keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"CourseViewPage.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"CourseViewPage.useEffect.handleKeyPress\": (event)=>{\n                    if (videos.length === 0) return;\n                    if (event.key === 'ArrowLeft' && currentVideoIndex > 0) {\n                        setCurrentVideoIndex(currentVideoIndex - 1);\n                    } else if (event.key === 'ArrowRight' && currentVideoIndex < videos.length - 1) {\n                        setCurrentVideoIndex(currentVideoIndex + 1);\n                    }\n                }\n            }[\"CourseViewPage.useEffect.handleKeyPress\"];\n            window.addEventListener('keydown', handleKeyPress);\n            return ({\n                \"CourseViewPage.useEffect\": ()=>window.removeEventListener('keydown', handleKeyPress)\n            })[\"CourseViewPage.useEffect\"];\n        }\n    }[\"CourseViewPage.useEffect\"], [\n        currentVideoIndex,\n        videos.length\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading course...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !course) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen bg-[#fafbfc]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-red-600\",\n                            children: error || 'Course not found'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    const currentVideo = videos[currentVideoIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-64px)] relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(leftSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-gray-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800\",\n                                                    children: \"Course Curriculum\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        videos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                videos.length,\n                                                \" modules\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto\",\n                                    children: videos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: [\n                                            videos.map((video, index)=>{\n                                                const moduleProgressData = moduleProgress.find((mp)=>mp.module_number === video.page_number);\n                                                const isCompleted = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.is_completed) || false;\n                                                const completionPercentage = (moduleProgressData === null || moduleProgressData === void 0 ? void 0 : moduleProgressData.completion_percentage) || 0;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(index === currentVideoIndex ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50', \" \").concat(isCompleted ? 'bg-green-50' : ''),\n                                                    onClick: ()=>{\n                                                        setCurrentVideoIndex(index);\n                                                        if (!hasStartedCourse) {\n                                                            startCourse();\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold \".concat(isCompleted ? 'bg-green-500 text-white' : index === currentVideoIndex ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'),\n                                                                children: isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 31\n                                                                }, this) : index === currentVideoIndex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 31\n                                                                }, this) : video.page_number\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-sm leading-tight \".concat(isCompleted ? 'text-green-800' : index === currentVideoIndex ? 'text-blue-800' : 'text-gray-800'),\n                                                                        children: [\n                                                                            \"Module \",\n                                                                            video.page_number,\n                                                                            \": \",\n                                                                            video.page_title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs mt-1 \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                        children: [\n                                                                            video.avatar_name,\n                                                                            \" • 30 min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs \".concat(isCompleted ? 'text-green-600' : index === currentVideoIndex ? 'text-blue-600' : 'text-gray-500'),\n                                                                                children: isCompleted ? 'Completed' : completionPercentage > 0 ? \"\".concat(Math.round(completionPercentage), \"% watched\") : 'Not started'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            completionPercentage > 0 && !isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1 bg-gray-200 rounded-full h-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-blue-500 h-1 rounded-full transition-all duration-300\",\n                                                                                    style: {\n                                                                                        width: \"\".concat(completionPercentage, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, video.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 rounded-lg border cursor-pointer transition-all \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-50 border-green-200 hover:bg-green-100' : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'),\n                                                onClick: ()=>{\n                                                    if ((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100) {\n                                                        setShowAssessmentModal(true);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'bg-green-100' : 'bg-gray-100'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-sm \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-800' : 'text-gray-500'),\n                                                                    children: \"Take Final Assessment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs \".concat((courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'text-green-600' : 'text-gray-400'),\n                                                                    children: (courseProgress === null || courseProgress === void 0 ? void 0 : courseProgress.progress_percentage) === 100 ? 'Click to start assessment' : 'Complete all modules to unlock assessment'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 text-center text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No curriculum available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setLeftSidebarVisible(!leftSidebarVisible),\n                            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                left: leftSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(leftSidebarVisible ? 'rotate-180' : ''),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black relative\",\n                                    children: [\n                                        videos.length > 0 && currentVideo && currentVideo.heygenbloburl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"w-full h-[400px] object-cover\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            onPlay: ()=>{\n                                                if (!hasStartedCourse) {\n                                                    startCourse();\n                                                }\n                                            },\n                                            onTimeUpdate: (e)=>{\n                                                const video = e.target;\n                                                const watchTime = Math.floor(video.currentTime);\n                                                const totalDuration = Math.floor(video.duration);\n                                                // Update progress every 10 seconds\n                                                if (watchTime % 10 === 0 && watchTime > 0) {\n                                                    updateModuleProgress(currentVideo.page_number, watchTime, totalDuration);\n                                                }\n                                            },\n                                            onEnded: ()=>{\n                                                const video = document.querySelector('video');\n                                                if (video) {\n                                                    const totalDuration = Math.floor(video.duration);\n                                                    updateModuleProgress(currentVideo.page_number, totalDuration, totalDuration, true);\n                                                }\n                                                // Auto-advance to next video after a short delay\n                                                setTimeout(()=>{\n                                                    if (currentVideoIndex < videos.length - 1) {\n                                                        setCurrentVideoIndex(currentVideoIndex + 1);\n                                                    }\n                                                }, 1500); // 1.5 second delay before auto-advancing\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                    src: currentVideo.heygenbloburl,\n                                                    type: \"video/mp4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Your browser does not support the video tag.\"\n                                            ]\n                                        }, currentVideo.heygenbloburl, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: videos.length === 0 ? 'No videos available for this course' : 'Loading video...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsQnAModalOpen(true),\n                                            className: \"absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10\",\n                                            title: \"Raise Hand\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 group-hover:animate-bounce\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-8 px-6\",\n                                        children: [\n                                            'Overview',\n                                            'Assessment',\n                                            'Discussion',\n                                            'Reviews'\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"py-4 font-medium border-b-2 transition-colors \".concat(activeTab === tab && tab !== 'Assessment' && tab !== 'Discussion' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                                onClick: ()=>{\n                                                    if (tab === 'Assessment') {\n                                                        setShowAssessmentModal(true);\n                                                    } else if (tab === 'Discussion') {\n                                                        router.push(\"/courses/\".concat(courseId, \"/discussion\"));\n                                                    } else {\n                                                        setActiveTab(tab);\n                                                    }\n                                                },\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 bg-white p-6 overflow-y-auto\",\n                                    children: [\n                                        activeTab === 'Overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"About This Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 mb-6\",\n                                                    children: \"Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"What you'll learn\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2 text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Industry best practices and standards\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Practical implementation strategies\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Real-world case studies and examples\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-500 mt-1\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 542,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Professional certification preparation\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Prerequisites\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700 mb-4\",\n                                                                    children: \"Advanced knowledge and experience required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-3\",\n                                                                    children: \"Course Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Domain: \",\n                                                                                course.domain\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Level: \",\n                                                                                course.level\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Duration: \",\n                                                                                course.estimated_hours,\n                                                                                \" hours\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Modules: \",\n                                                                                videos.length\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Certificate: Available upon completion\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        activeTab === 'Reviews' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-8\",\n                                            children: \"Student reviews coming soon...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setRightSidebarVisible(!rightSidebarVisible),\n                            className: \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200\",\n                            style: {\n                                right: rightSidebarVisible ? '320px' : '0px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 transition-transform duration-200 \".concat(rightSidebarVisible ? '' : 'rotate-180'),\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(rightSidebarVisible ? 'w-80' : 'w-0', \" bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-2\",\n                                            children: \"Course Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-3\",\n                                            children: course.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium\",\n                                                children: course.domain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-yellow-400\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"4.8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"(234 reviews)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-600 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                course.estimated_hours,\n                                                                \" hours\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                videos.length,\n                                                                \" students\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: course.level\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Certificate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                videos.length > 0 && courseProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-gray-500 mb-3\",\n                                            children: \"Course Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Overall Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold text-orange-600\",\n                                                            children: [\n                                                                Math.round(courseProgress.progress_percentage),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2 mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(courseProgress.progress_percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 text-xs text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                courseProgress.completed_modules,\n                                                                \" of \",\n                                                                courseProgress.total_modules,\n                                                                \" modules completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Status: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium \".concat(courseProgress.status === 'completed' ? 'text-green-600' : courseProgress.status === 'in_progress' ? 'text-blue-600' : 'text-gray-600'),\n                                                                    children: courseProgress.status === 'completed' ? 'Completed' : courseProgress.status === 'in_progress' ? 'In Progress' : 'Not Started'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        courseProgress.started_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(courseProgress.started_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        courseProgress.completed_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Completed: \",\n                                                                new Date(courseProgress.completed_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-blue-800 mb-1\",\n                                                    children: \"Currently Watching\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Module \",\n                                                        currentVideo.page_number,\n                                                        \": \",\n                                                        currentVideo.page_title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: currentVideo.avatar_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QnAWithHeyGenModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: isQnAModalOpen,\n                onClose: ()=>setIsQnAModalOpen(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AssessmentModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAssessmentModal,\n                onClose: ()=>setShowAssessmentModal(false),\n                courseId: courseId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\courses\\\\[id]\\\\page.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseViewPage, \"RVk41ZQYMnmK3mH1gQElnMAZbYc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = CourseViewPage;\nvar _c;\n$RefreshReg$(_c, \"CourseViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY291cnNlcy9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFZ0Q7QUFFd0I7QUFDTjtBQUNGO0FBQ1Q7QUE0QnhDLFNBQVNTOztJQUN0QixNQUFNQyxTQUFTSCwwREFBU0E7SUFDeEIsTUFBTUksU0FBU0gsMERBQVNBO0lBQ3hCLE1BQU1JLFdBQVdGLE9BQU9HLEVBQUU7SUFFMUIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdYLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1kscUJBQXFCQyx1QkFBdUIsR0FBR2IsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDYyxRQUFRQyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFnQjtJQUNwRCxNQUFNLENBQUNnQixRQUFRQyxVQUFVLEdBQUdqQiwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUN0RCxNQUFNLENBQUNrQixtQkFBbUJDLHFCQUFxQixHQUFHbkIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDb0IsU0FBU0MsV0FBVyxHQUFHckIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDc0IsT0FBT0MsU0FBUyxHQUFHdkIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3dCLG9CQUFvQkMsc0JBQXNCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUMwQixxQkFBcUJDLHVCQUF1QixHQUFHM0IsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDNEIsa0JBQWtCQyxvQkFBb0IsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhCLGdCQUFnQkMsa0JBQWtCLEdBQUcvQiwrQ0FBUUEsQ0FBTTtJQUMxRCxNQUFNLENBQUNnQyxnQkFBZ0JDLGtCQUFrQixHQUFHakMsK0NBQVFBLENBQVEsRUFBRTtJQUM5RCxNQUFNLENBQUNrQyxhQUFhQyxlQUFlLEdBQUduQywrQ0FBUUEsQ0FBTTtJQUNwRCxNQUFNLENBQUNvQyxnQkFBZ0JDLGtCQUFrQixHQUFHckMsK0NBQVFBLENBQUM7SUFHckQsNkNBQTZDO0lBQzdDLE1BQU1zQyxtQkFBbUI7UUFDdkIsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1FBQ25DLElBQUlGLE9BQU87WUFDVCxJQUFJO2dCQUNGLE1BQU1HLFlBQVlILE1BQU1JLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDckMsTUFBTUMsU0FBU0YsVUFBVUcsT0FBTyxDQUFDLE1BQU0sS0FBS0EsT0FBTyxDQUFDLE1BQU07Z0JBQzFELE1BQU1DLGNBQWNDLG1CQUFtQkMsS0FBS0osUUFBUUQsS0FBSyxDQUFDLElBQUlNLEdBQUcsQ0FBQyxTQUFTQyxDQUFDO29CQUMxRSxPQUFPLE1BQU0sQ0FBQyxPQUFPQSxFQUFFQyxVQUFVLENBQUMsR0FBR0MsUUFBUSxDQUFDLEdBQUUsRUFBR0MsS0FBSyxDQUFDLENBQUM7Z0JBQzVELEdBQUdDLElBQUksQ0FBQztnQkFDUixPQUFPQyxLQUFLQyxLQUFLLENBQUNWO1lBQ3BCLEVBQUUsT0FBT3hCLE9BQU87Z0JBQ2RtQyxRQUFRbkMsS0FBSyxDQUFDLHlCQUF5QkE7Z0JBQ3ZDLE9BQU87WUFDVDtRQUNGO1FBQ0EsT0FBTztJQUNUO0lBRUEsNkJBQTZCO0lBQzdCckIsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXlELE9BQU9wQjtZQUNiLElBQUlvQixNQUFNO2dCQUNSdkIsZUFBZXVCO1lBQ2pCLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Q0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7WUFDekI7UUFDRjttQ0FBRyxFQUFFO0lBRUwsTUFBTUMsU0FBUzVCLHdCQUFBQSxrQ0FBQUEsWUFBYXpCLEVBQUU7SUFFOUIsc0JBQXNCO0lBQ3RCLE1BQU1zRCxvQkFBb0I3RCxrREFBV0E7eURBQUM7WUFDcEMsSUFBSSxDQUFDNEQsUUFBUSxRQUFRLDRCQUE0QjtZQUVqRCxJQUFJO2dCQUNGLE1BQU1FLG1CQUFtQixNQUFNQyxNQUFNLG1DQUFxRHpELE9BQWxCc0QsUUFBTyxhQUFvQixPQUFUdEQsVUFBUztnQkFDbkcsSUFBSXdELGlCQUFpQkUsRUFBRSxFQUFFO29CQUN2QixNQUFNQyxlQUFlLE1BQU1ILGlCQUFpQkksSUFBSTtvQkFDaERyQyxrQkFBa0JvQyxhQUFhckMsY0FBYztvQkFDN0NHLGtCQUFrQmtDLGFBQWFuQyxjQUFjO29CQUU3Qyw0Q0FBNEM7b0JBQzVDLElBQUltQyxhQUFhckMsY0FBYyxJQUFJcUMsYUFBYXJDLGNBQWMsQ0FBQ3VDLGNBQWMsR0FBRyxHQUFHO3dCQUNqRmxELHFCQUFxQmdELGFBQWFyQyxjQUFjLENBQUN1QyxjQUFjLEdBQUc7b0JBQ3BFO2dCQUNGO1lBQ0YsRUFBRSxPQUFPL0MsT0FBTztnQkFDZG1DLFFBQVFuQyxLQUFLLENBQUMsNEJBQTRCQTtZQUM1QztRQUNGO3dEQUFHO1FBQUN3QztRQUFRdEQ7S0FBUztJQUVyQixxREFBcUQ7SUFDckQsTUFBTThELGNBQWNwRSxrREFBV0E7bURBQUM7WUFDOUIsSUFBSSxDQUFDNEQsUUFBUSxRQUFRLDRCQUE0QjtZQUVqRCxJQUFJO2dCQUNGLE1BQU1TLFdBQVcsTUFBTU4sTUFBTSxxQ0FBOEMsT0FBVHpELFVBQVMsV0FBUztvQkFDbEZnRSxRQUFRO29CQUNSQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7b0JBQ0FDLE1BQU1uQixLQUFLb0IsU0FBUyxDQUFDO3dCQUFFYjtvQkFBTztnQkFDaEM7Z0JBRUEsSUFBSVMsU0FBU0wsRUFBRSxFQUFFO29CQUNmckMsb0JBQW9CO29CQUNwQixNQUFNa0MscUJBQXFCLHdCQUF3QjtvQkFDbkROLFFBQVFtQixHQUFHLENBQUMsaUNBQWlDcEU7Z0JBQy9DO1lBQ0YsRUFBRSxPQUFPYyxPQUFPO2dCQUNkbUMsUUFBUW5DLEtBQUssQ0FBQywwQkFBMEJBO1lBQzFDO1FBQ0Y7a0RBQUc7UUFBQ3dDO1FBQVF0RDtRQUFVdUQ7S0FBa0I7SUFFeEMscUNBQXFDO0lBQ3JDLE1BQU1jLHVCQUF1QjNFLGtEQUFXQTs0REFBQyxlQUFPNEUsY0FBc0JDLFdBQW1CQztnQkFBdUJDLCtFQUF1QjtZQUNySSxJQUFJLENBQUNuQixRQUFRLFFBQVEsNkJBQTZCO1lBRWxELElBQUk7Z0JBQ0YsTUFBTW9CLGVBQWVsRSxNQUFNLENBQUM4RCxlQUFlLEVBQUU7Z0JBQzdDLE1BQU1LLGNBQWM7b0JBQ2xCckI7b0JBQ0FpQjtvQkFDQUM7b0JBQ0FDO29CQUNBRyxhQUFhRixDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNHLFVBQVUsS0FBSSxVQUF1QixPQUFiUDtnQkFDckQ7Z0JBRUFyQixRQUFRbUIsR0FBRyxDQUFDLDZCQUE2Qk87Z0JBRXpDLE1BQU1aLFdBQVcsTUFBTU4sTUFBTSxxQ0FBeURhLE9BQXBCdEUsVUFBUyxhQUF3QixPQUFic0UsY0FBYSxjQUFZO29CQUM3R04sUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNbkIsS0FBS29CLFNBQVMsQ0FBQ1E7Z0JBQ3ZCO2dCQUVBLElBQUlaLFNBQVNMLEVBQUUsRUFBRTtvQkFDZixNQUFNb0IsU0FBUyxNQUFNZixTQUFTSCxJQUFJO29CQUNsQ1gsUUFBUW1CLEdBQUcsQ0FBQywrQkFBK0JVO29CQUMzQyxNQUFNdkIscUJBQXFCLHdCQUF3QjtnQkFDckQsT0FBTztvQkFDTCxNQUFNd0IsWUFBWSxNQUFNaEIsU0FBU0gsSUFBSTtvQkFDckNYLFFBQVFuQyxLQUFLLENBQUMsMkJBQTJCaUU7Z0JBQzNDO1lBQ0YsRUFBRSxPQUFPakUsT0FBTztnQkFDZG1DLFFBQVFuQyxLQUFLLENBQUMsbUNBQW1DQTtZQUNuRDtRQUNGOzJEQUFHO1FBQUN3QztRQUFRdEQ7UUFBVVE7UUFBUStDO0tBQWtCO0lBSWhEOUQsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTXVGOzREQUFrQjtvQkFDdEIsSUFBSTt3QkFDRm5FLFdBQVc7d0JBRVgsdUJBQXVCO3dCQUN2QixNQUFNb0UsaUJBQWlCLE1BQU14QixNQUFNLHFDQUE4QyxPQUFUekQ7d0JBQ3hFLElBQUksQ0FBQ2lGLGVBQWV2QixFQUFFLEVBQUU7NEJBQ3RCLE1BQU0sSUFBSXdCLE1BQU07d0JBQ2xCO3dCQUNBLE1BQU1DLGFBQWEsTUFBTUYsZUFBZXJCLElBQUk7d0JBQzVDckQsVUFBVTRFO3dCQUVWLHNCQUFzQjt3QkFDdEIsTUFBTUMsaUJBQWlCLE1BQU0zQixNQUFNLHFDQUE4QyxPQUFUekQsVUFBUzt3QkFDakYsSUFBSSxDQUFDb0YsZUFBZTFCLEVBQUUsRUFBRTs0QkFDdEIsTUFBTSxJQUFJd0IsTUFBTTt3QkFDbEI7d0JBQ0EsTUFBTUcsYUFBYSxNQUFNRCxlQUFleEIsSUFBSTt3QkFDNUNuRCxVQUFVNEU7d0JBRVYsc0VBQXNFO3dCQUN0RSxJQUFJL0IsUUFBUTs0QkFDVixNQUFNQzt3QkFDUjtvQkFFRixFQUFFLE9BQU8rQixLQUFjO3dCQUNyQnZFLFNBQVN1RSxlQUFlSixRQUFRSSxJQUFJQyxPQUFPLEdBQUc7b0JBQ2hELFNBQVU7d0JBQ1IxRSxXQUFXO29CQUNiO2dCQUNGOztZQUVBLElBQUliLFlBQVlzRCxRQUFRO2dCQUN0QjBCO1lBQ0Y7UUFDRjttQ0FBRztRQUFDaEY7UUFBVXNEO0tBQU87SUFFckIsc0JBQXNCO0lBQ3RCN0QsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTStGOzJEQUFpQixDQUFDQztvQkFDdEIsSUFBSWpGLE9BQU9rRixNQUFNLEtBQUssR0FBRztvQkFFekIsSUFBSUQsTUFBTUUsR0FBRyxLQUFLLGVBQWVqRixvQkFBb0IsR0FBRzt3QkFDdERDLHFCQUFxQkQsb0JBQW9CO29CQUMzQyxPQUFPLElBQUkrRSxNQUFNRSxHQUFHLEtBQUssZ0JBQWdCakYsb0JBQW9CRixPQUFPa0YsTUFBTSxHQUFHLEdBQUc7d0JBQzlFL0UscUJBQXFCRCxvQkFBb0I7b0JBQzNDO2dCQUNGOztZQUVBeUMsT0FBT3lDLGdCQUFnQixDQUFDLFdBQVdKO1lBQ25DOzRDQUFPLElBQU1yQyxPQUFPMEMsbUJBQW1CLENBQUMsV0FBV0w7O1FBQ3JEO21DQUFHO1FBQUM5RTtRQUFtQkYsT0FBT2tGLE1BQU07S0FBQztJQUVyQyxJQUFJOUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDa0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUMzRywwREFBTUE7Ozs7OzhCQUNQLDhEQUFDNEc7b0JBQUtELFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLakQ7SUFFQSxJQUFJakYsU0FBUyxDQUFDUixRQUFRO1FBQ3BCLHFCQUNFLDhEQUFDd0Y7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUMzRywwREFBTUE7Ozs7OzhCQUNQLDhEQUFDNEc7b0JBQUtELFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWmpGLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdEI7SUFFQSxNQUFNNEQsZUFBZWxFLE1BQU0sQ0FBQ0Usa0JBQWtCO0lBRTlDLHFCQUNFLDhEQUFDb0Y7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUMzRywwREFBTUE7Ozs7OzBCQUNQLDhEQUFDNEc7Z0JBQUtELFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVcsR0FBdUMsT0FBcEMvRSxxQkFBcUIsU0FBUyxPQUFNOzs4Q0FDckQsOERBQUM4RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUlGLFdBQVU7b0RBQXdCRyxNQUFLO29EQUFlQyxTQUFROzhEQUNqRSw0RUFBQ0M7d0RBQUtDLFVBQVM7d0RBQVVDLEdBQUU7d0RBQW1RQyxVQUFTOzs7Ozs7Ozs7Ozs4REFFelMsOERBQUNDO29EQUFHVCxXQUFVOzhEQUFzQzs7Ozs7Ozs7Ozs7O3dDQUVyRHZGLE9BQU9rRixNQUFNLEdBQUcsbUJBQ2YsOERBQUNlOzRDQUFFVixXQUFVOztnREFBeUJ2RixPQUFPa0YsTUFBTTtnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FJeEQsOERBQUNJO29DQUFJQyxXQUFVOzhDQUNadkYsT0FBT2tGLE1BQU0sR0FBRyxrQkFDZiw4REFBQ0k7d0NBQUlDLFdBQVU7OzRDQUNadkYsT0FBT2lDLEdBQUcsQ0FBQyxDQUFDaUUsT0FBT0M7Z0RBQ2xCLE1BQU1DLHFCQUFxQnBGLGVBQWVxRixJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdDLGFBQWEsS0FBS0wsTUFBTU0sV0FBVztnREFDM0YsTUFBTXZDLGNBQWNtQyxDQUFBQSwrQkFBQUEseUNBQUFBLG1CQUFvQkssWUFBWSxLQUFJO2dEQUN4RCxNQUFNQyx1QkFBdUJOLENBQUFBLCtCQUFBQSx5Q0FBQUEsbUJBQW9CTyxxQkFBcUIsS0FBSTtnREFFMUUscUJBQ0UsOERBQUNyQjtvREFFQ0MsV0FBVyxrRUFJUHRCLE9BSEZrQyxVQUFVakcsb0JBQ04sMENBQ0Esb0JBQ0wsS0FBb0MsT0FBakMrRCxjQUFjLGdCQUFnQjtvREFDbEMyQyxTQUFTO3dEQUNQekcscUJBQXFCZ0c7d0RBQ3JCLElBQUksQ0FBQ3ZGLGtCQUFrQjs0REFDckIwQzt3REFDRjtvREFDRjs4REFFQSw0RUFBQ2dDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVcsNkZBTWYsT0FMQ3RCLGNBQ0ksNEJBQ0FrQyxVQUFVakcsb0JBQ1IsMkJBQ0E7MEVBRUwrRCw0QkFDQyw4REFBQ3dCO29FQUFJRixXQUFVO29FQUFVRyxNQUFLO29FQUFlQyxTQUFROzhFQUNuRCw0RUFBQ0M7d0VBQUtDLFVBQVM7d0VBQVVDLEdBQUU7d0VBQXFIQyxVQUFTOzs7Ozs7Ozs7OzJFQUV6SkksVUFBVWpHLGtDQUNaLDhEQUFDdUY7b0VBQUlGLFdBQVU7b0VBQVVHLE1BQUs7b0VBQWVDLFNBQVE7OEVBQ25ELDRFQUFDQzt3RUFBS0MsVUFBUzt3RUFBVUMsR0FBRTt3RUFBMEdDLFVBQVM7Ozs7Ozs7Ozs7MkVBR2hKRyxNQUFNTSxXQUFXOzs7Ozs7MEVBR3JCLDhEQUFDbEI7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDc0I7d0VBQUd0QixXQUFXLHFDQUdkLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7OzRFQUM5Qzs0RUFDTWdHLE1BQU1NLFdBQVc7NEVBQUM7NEVBQUdOLE1BQU03QixVQUFVOzs7Ozs7O2tGQUUvQyw4REFBQzRCO3dFQUFFVixXQUFXLGdCQUdiLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7OzRFQUUvQ2dHLE1BQU1ZLFdBQVc7NEVBQUM7Ozs7Ozs7a0ZBRXJCLDhEQUFDeEI7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDVTtnRkFBRVYsV0FBVyxXQUdiLE9BRkN0QixjQUFjLG1CQUNka0MsVUFBVWpHLG9CQUFvQixrQkFBa0I7MEZBRS9DK0QsY0FBYyxjQUFjeUMsdUJBQXVCLElBQUksR0FBb0MsT0FBakNLLEtBQUtDLEtBQUssQ0FBQ04sdUJBQXNCLGVBQWE7Ozs7Ozs0RUFFMUdBLHVCQUF1QixLQUFLLENBQUN6Qyw2QkFDNUIsOERBQUNxQjtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ0Q7b0ZBQ0NDLFdBQVU7b0ZBQ1YwQixPQUFPO3dGQUFFQyxPQUFPLEdBQXdCLE9BQXJCUixzQkFBcUI7b0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQXpEbERSLE1BQU16RyxFQUFFOzs7Ozs0Q0FrRW5COzBEQUdBLDhEQUFDNkY7Z0RBQ0NDLFdBQVcsNERBSVYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxvREFDQTtnREFFTlAsU0FBUztvREFDUCxJQUFJOUYsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLEtBQUs7d0RBQy9DdEgsdUJBQXVCO29EQUN6QjtnREFDRjswREFFQSw0RUFBQ3lGO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVcseURBSWYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxpQkFDQTtzRUFFSiw0RUFBQzFCO2dFQUFJRixXQUFXLFdBSWYsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxtQkFDQTtnRUFDRnpCLE1BQUs7Z0VBQWVDLFNBQVE7MEVBQzlCLDRFQUFDQztvRUFBS0MsVUFBUztvRUFBVUMsR0FBRTtvRUFBcUhDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBRzdKLDhEQUFDVDs7OEVBQ0MsOERBQUN1QjtvRUFBR3RCLFdBQVcsdUJBSWQsT0FIQ3pFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxRyxtQkFBbUIsTUFBSyxNQUNwQyxtQkFDQTs4RUFDRjs7Ozs7OzhFQUNKLDhEQUFDbEI7b0VBQUVWLFdBQVcsV0FJYixPQUhDekUsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLE1BQ3BDLG1CQUNBOzhFQUVIckcsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnFHLG1CQUFtQixNQUFLLE1BQ3JDLDhCQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQU9kLDhEQUFDN0I7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNVO3NEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9YLDhEQUFDbUI7NEJBQ0NSLFNBQVMsSUFBTW5HLHNCQUFzQixDQUFDRDs0QkFDdEMrRSxXQUFVOzRCQUNWMEIsT0FBTztnQ0FBRUksTUFBTTdHLHFCQUFxQixVQUFVOzRCQUFNO3NDQUVwRCw0RUFBQ2lGO2dDQUNDRixXQUFXLDZDQUFvRixPQUF2Qy9FLHFCQUFxQixlQUFlO2dDQUM1RmtGLE1BQUs7Z0NBQ0xDLFNBQVE7MENBRVIsNEVBQUNDO29DQUFLQyxVQUFTO29DQUFVQyxHQUFFO29DQUFxSEMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLN0osOERBQUNUOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3dDQUNadkYsT0FBT2tGLE1BQU0sR0FBRyxLQUFLaEIsZ0JBQWdCQSxhQUFhb0QsYUFBYSxpQkFDOUQsOERBQUNwQjs0Q0FFQ1gsV0FBVTs0Q0FDVmdDLFFBQVE7NENBQ1JDLFFBQVE7NENBQ1JDLFFBQVE7Z0RBQ04sSUFBSSxDQUFDN0csa0JBQWtCO29EQUNyQjBDO2dEQUNGOzRDQUNGOzRDQUNBb0UsY0FBYyxDQUFDQztnREFDYixNQUFNekIsUUFBUXlCLEVBQUVDLE1BQU07Z0RBQ3RCLE1BQU03RCxZQUFZZ0QsS0FBS2MsS0FBSyxDQUFDM0IsTUFBTTRCLFdBQVc7Z0RBQzlDLE1BQU05RCxnQkFBZ0IrQyxLQUFLYyxLQUFLLENBQUMzQixNQUFNNkIsUUFBUTtnREFFL0MsbUNBQW1DO2dEQUNuQyxJQUFJaEUsWUFBWSxPQUFPLEtBQUtBLFlBQVksR0FBRztvREFDekNGLHFCQUFxQkssYUFBYXNDLFdBQVcsRUFBRXpDLFdBQVdDO2dEQUM1RDs0Q0FDRjs0Q0FDQWdFLFNBQVM7Z0RBQ1AsTUFBTTlCLFFBQVErQixTQUFTQyxhQUFhLENBQUM7Z0RBQ3JDLElBQUloQyxPQUFPO29EQUNULE1BQU1sQyxnQkFBZ0IrQyxLQUFLYyxLQUFLLENBQUMzQixNQUFNNkIsUUFBUTtvREFDL0NsRSxxQkFBcUJLLGFBQWFzQyxXQUFXLEVBQUV4QyxlQUFlQSxlQUFlO2dEQUMvRTtnREFFQSxpREFBaUQ7Z0RBQ2pEbUUsV0FBVztvREFDVCxJQUFJakksb0JBQW9CRixPQUFPa0YsTUFBTSxHQUFHLEdBQUc7d0RBQ3pDL0UscUJBQXFCRCxvQkFBb0I7b0RBQzNDO2dEQUNGLEdBQUcsT0FBTyx5Q0FBeUM7NENBQ3JEOzs4REFFQSw4REFBQ2tJO29EQUFPQyxLQUFLbkUsYUFBYW9ELGFBQWE7b0RBQUVnQixNQUFLOzs7Ozs7Z0RBQWM7OzJDQWxDdkRwRSxhQUFhb0QsYUFBYTs7OztpRUFzQ2pDLDhEQUFDaEM7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNnRDtnREFBS2hELFdBQVU7MERBQ2J2RixPQUFPa0YsTUFBTSxLQUFLLElBQUksd0NBQXdDOzs7Ozs7Ozs7OztzREFNckUsOERBQUNrQzs0Q0FDQ1IsU0FBUyxJQUFNdkYsa0JBQWtCOzRDQUNqQ2tFLFdBQVU7NENBQ1ZpRCxPQUFNO3NEQUVOLDRFQUFDL0M7Z0RBQ0NGLFdBQVU7Z0RBQ1ZHLE1BQUs7Z0RBQ0xDLFNBQVE7MERBRVIsNEVBQUNDO29EQUFLQyxVQUFTO29EQUFVQyxHQUFFO29EQUFrS0MsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNNU0sOERBQUNUO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjs0Q0FBQzs0Q0FBWTs0Q0FBYzs0Q0FBYzt5Q0FBVSxDQUFDdEQsR0FBRyxDQUFDLENBQUN3RyxvQkFDeEQsOERBQUNyQjtnREFFQzdCLFdBQVcsaURBSVYsT0FIQzdGLGNBQWMrSSxPQUFPQSxRQUFRLGdCQUFnQkEsUUFBUSxlQUNqRCxzQ0FDQTtnREFFTjdCLFNBQVM7b0RBQ1AsSUFBSTZCLFFBQVEsY0FBYzt3REFDeEI1SSx1QkFBdUI7b0RBQ3pCLE9BQU8sSUFBSTRJLFFBQVEsY0FBYzt3REFDL0JsSixPQUFPbUosSUFBSSxDQUFDLFlBQXFCLE9BQVRsSixVQUFTO29EQUNuQyxPQUFPO3dEQUNMRyxhQUFhOEk7b0RBQ2Y7Z0RBQ0Y7MERBRUNBOytDQWhCSUE7Ozs7Ozs7Ozs7Ozs7Ozs4Q0F1QmIsOERBQUNuRDtvQ0FBSUMsV0FBVTs7d0NBQ1o3RixjQUFjLDRCQUNiOzs4REFDRSw4REFBQ3NHO29EQUFHVCxXQUFVOzhEQUEwQjs7Ozs7OzhEQUN4Qyw4REFBQ1U7b0RBQUVWLFdBQVU7OERBQXFCOzs7Ozs7OERBSWxDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQ3VCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNvRDtvRUFBR3BELFdBQVU7O3NGQUNaLDhEQUFDcUQ7NEVBQUdyRCxXQUFVOzs4RkFDWiw4REFBQ2dEO29GQUFLaEQsV0FBVTs4RkFBc0I7Ozs7OztnRkFBUTs7Ozs7OztzRkFHaEQsOERBQUNxRDs0RUFBR3JELFdBQVU7OzhGQUNaLDhEQUFDZ0Q7b0ZBQUtoRCxXQUFVOzhGQUFzQjs7Ozs7O2dGQUFROzs7Ozs7O3NGQUdoRCw4REFBQ3FEOzRFQUFHckQsV0FBVTs7OEZBQ1osOERBQUNnRDtvRkFBS2hELFdBQVU7OEZBQXNCOzs7Ozs7Z0ZBQVE7Ozs7Ozs7c0ZBR2hELDhEQUFDcUQ7NEVBQUdyRCxXQUFVOzs4RkFDWiw4REFBQ2dEO29GQUFLaEQsV0FBVTs4RkFBc0I7Ozs7OztnRkFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFNcEQsOERBQUNEOzs4RUFDQyw4REFBQ3VCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNVO29FQUFFVixXQUFVOzhFQUFxQjs7Ozs7OzhFQUlsQyw4REFBQ3NCO29FQUFHdEIsV0FBVTs4RUFBNkI7Ozs7Ozs4RUFDM0MsOERBQUNvRDtvRUFBR3BELFdBQVU7O3NGQUNaLDhEQUFDcUQ7O2dGQUFHO2dGQUFTOUksT0FBTytJLE1BQU07Ozs7Ozs7c0ZBQzFCLDhEQUFDRDs7Z0ZBQUc7Z0ZBQVE5SSxPQUFPZ0osS0FBSzs7Ozs7OztzRkFDeEIsOERBQUNGOztnRkFBRztnRkFBVzlJLE9BQU9pSixlQUFlO2dGQUFDOzs7Ozs7O3NGQUN0Qyw4REFBQ0g7O2dGQUFHO2dGQUFVNUksT0FBT2tGLE1BQU07Ozs7Ozs7c0ZBQzNCLDhEQUFDMEQ7c0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQVdibEosY0FBYywyQkFDYiw4REFBQzRGOzRDQUFJQyxXQUFVO3NEQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU10RCw4REFBQzZCOzRCQUNDUixTQUFTLElBQU1qRyx1QkFBdUIsQ0FBQ0Q7NEJBQ3ZDNkUsV0FBVTs0QkFDVjBCLE9BQU87Z0NBQUUrQixPQUFPdEksc0JBQXNCLFVBQVU7NEJBQU07c0NBRXRELDRFQUFDK0U7Z0NBQ0NGLFdBQVcsNkNBQXFGLE9BQXhDN0Usc0JBQXNCLEtBQUs7Z0NBQ25GZ0YsTUFBSztnQ0FDTEMsU0FBUTswQ0FFUiw0RUFBQ0M7b0NBQUtDLFVBQVM7b0NBQVVDLEdBQUU7b0NBQXFIQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7O3NDQUs3Siw4REFBQ1Q7NEJBQUlDLFdBQVcsR0FBd0MsT0FBckM3RSxzQkFBc0IsU0FBUyxPQUFNOzs4Q0FDdEQsOERBQUM0RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNzQjs0Q0FBR3RCLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDUzs0Q0FBR1QsV0FBVTtzREFBd0N6RixPQUFPMEksS0FBSzs7Ozs7O3NEQUVsRSw4REFBQ2xEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDZ0Q7Z0RBQUtoRCxXQUFVOzBEQUNiekYsT0FBTytJLE1BQU07Ozs7Ozs7Ozs7O3NEQUlsQiw4REFBQ3ZEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFO3dEQUFJRixXQUFVO3dEQUEwQkcsTUFBSzt3REFBZUMsU0FBUTtrRUFDbkUsNEVBQUNDOzREQUFLRSxHQUFFOzs7Ozs7Ozs7OztrRUFFViw4REFBQ3lDO3dEQUFLaEQsV0FBVTtrRUFBYzs7Ozs7O2tFQUM5Qiw4REFBQ2dEO2tFQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztzREFJViw4REFBQ2pEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBSUYsV0FBVTs0REFBVUcsTUFBSzs0REFBZUMsU0FBUTtzRUFDbkQsNEVBQUNDO2dFQUFLQyxVQUFTO2dFQUFVQyxHQUFFO2dFQUFxSEMsVUFBUzs7Ozs7Ozs7Ozs7c0VBRTNKLDhEQUFDd0M7O2dFQUFNekksT0FBT2lKLGVBQWU7Z0VBQUM7Ozs7Ozs7Ozs7Ozs7OERBRWhDLDhEQUFDekQ7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBSUYsV0FBVTs0REFBVUcsTUFBSzs0REFBZUMsU0FBUTtzRUFDbkQsNEVBQUNDO2dFQUFLRSxHQUFFOzs7Ozs7Ozs7OztzRUFFViw4REFBQ3lDOztnRUFBTXZJLE9BQU9rRixNQUFNO2dFQUFDOzs7Ozs7Ozs7Ozs7OzhEQUV2Qiw4REFBQ0k7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBSUYsV0FBVTs0REFBVUcsTUFBSzs0REFBZUMsU0FBUTtzRUFDbkQsNEVBQUNDO2dFQUFLQyxVQUFTO2dFQUFVQyxHQUFFO2dFQUFtUUMsVUFBUzs7Ozs7Ozs7Ozs7c0VBRXpTLDhEQUFDd0M7c0VBQU16SSxPQUFPZ0osS0FBSzs7Ozs7Ozs7Ozs7OzhEQUVyQiw4REFBQ3hEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0U7NERBQUlGLFdBQVU7NERBQVVHLE1BQUs7NERBQWVDLFNBQVE7c0VBQ25ELDRFQUFDQztnRUFBS0MsVUFBUztnRUFBVUMsR0FBRTtnRUFBa2lCQyxVQUFTOzs7Ozs7Ozs7OztzRUFFeGtCLDhEQUFDd0M7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0F1Qlh2SSxPQUFPa0YsTUFBTSxHQUFHLEtBQUtwRSxnQ0FDcEIsOERBQUN3RTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMwRDs0Q0FBRzFELFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2dEOzREQUFLaEQsV0FBVTtzRUFBb0M7Ozs7OztzRUFDcEQsOERBQUNnRDs0REFBS2hELFdBQVU7O2dFQUNid0IsS0FBS0MsS0FBSyxDQUFDbEcsZUFBZXFHLG1CQUFtQjtnRUFBRTs7Ozs7Ozs7Ozs7Ozs4REFHcEQsOERBQUM3QjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQ0NDLFdBQVU7d0RBQ1YwQixPQUFPOzREQUFFQyxPQUFPLEdBQXNDLE9BQW5DcEcsZUFBZXFHLG1CQUFtQixFQUFDO3dEQUFHOzs7Ozs7Ozs7Ozs4REFHN0QsOERBQUM3QjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNVOztnRUFBR25GLGVBQWVvSSxpQkFBaUI7Z0VBQUM7Z0VBQUtwSSxlQUFlcUksYUFBYTtnRUFBQzs7Ozs7OztzRUFDdkUsOERBQUNsRDs7Z0VBQUU7OEVBQVEsOERBQUNzQztvRUFBS2hELFdBQVcsZUFHM0IsT0FGQ3pFLGVBQWVzSSxNQUFNLEtBQUssY0FBYyxtQkFDeEN0SSxlQUFlc0ksTUFBTSxLQUFLLGdCQUFnQixrQkFBa0I7OEVBRTNEdEksZUFBZXNJLE1BQU0sS0FBSyxjQUFjLGNBQ3hDdEksZUFBZXNJLE1BQU0sS0FBSyxnQkFBZ0IsZ0JBQWdCOzs7Ozs7Ozs7Ozs7d0RBRTVEdEksZUFBZXVJLFVBQVUsa0JBQ3hCLDhEQUFDcEQ7O2dFQUFFO2dFQUFVLElBQUlxRCxLQUFLeEksZUFBZXVJLFVBQVUsRUFBRUUsa0JBQWtCOzs7Ozs7O3dEQUVwRXpJLGVBQWUwSSxZQUFZLGtCQUMxQiw4REFBQ3ZEOztnRUFBRTtnRUFBWSxJQUFJcUQsS0FBS3hJLGVBQWUwSSxZQUFZLEVBQUVELGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FNNUVyRiw4QkFDQyw4REFBQ29COzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2tFO29EQUFHbEUsV0FBVTs4REFBeUM7Ozs7Ozs4REFDdkQsOERBQUNVO29EQUFFVixXQUFVOzt3REFBd0I7d0RBQzNCckIsYUFBYXNDLFdBQVc7d0RBQUM7d0RBQUd0QyxhQUFhRyxVQUFVOzs7Ozs7OzhEQUU3RCw4REFBQzRCO29EQUFFVixXQUFVOzhEQUNWckIsYUFBYTRDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVd6Qyw4REFBQ2pJLHNFQUFrQkE7Z0JBQ2pCNkssUUFBUXRJO2dCQUNSdUksU0FBUyxJQUFNdEksa0JBQWtCO2dCQUNqQzdCLFVBQVVBOzs7Ozs7MEJBSVosOERBQUNWLG1FQUFlQTtnQkFDZDRLLFFBQVE5SjtnQkFDUitKLFNBQVMsSUFBTTlKLHVCQUF1QjtnQkFDdENMLFVBQVVBOzs7Ozs7Ozs7Ozs7QUFJbEI7R0FuckJ3Qkg7O1FBQ1BGLHNEQUFTQTtRQUNUQyxzREFBU0E7OztLQUZGQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx3aW5kb3dzdm0yXFxEZXNrdG9wXFxDaG5hZHJ1Y29kZVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxjb3Vyc2VzXFxbaWRdXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgTmF2YmFyIGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvTmF2YmFyJztcclxuaW1wb3J0IERpc2N1c3Npb25Gb3J1bSBmcm9tICcuLi8uLi8uLi9jb21wb25lbnRzL0Rpc2N1c3Npb25Gb3J1bSc7XHJcbmltcG9ydCBRbkFXaXRoSGV5R2VuTW9kYWwgZnJvbSAnLi4vLi4vLi4vY29tcG9uZW50cy9RbkFXaXRoSGV5R2VuTW9kYWwnO1xyXG5pbXBvcnQgQXNzZXNzbWVudE1vZGFsIGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvQXNzZXNzbWVudE1vZGFsJztcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcblxyXG5pbnRlcmZhY2UgQ291cnNlIHtcclxuICBpZDogbnVtYmVyO1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBkb21haW46IHN0cmluZztcclxuICBsZXZlbDogc3RyaW5nO1xyXG4gIGVzdGltYXRlZF9ob3VyczogbnVtYmVyO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIENvdXJzZVZpZGVvIHtcclxuICBpZDogbnVtYmVyO1xyXG4gIHBhZ2VfbnVtYmVyOiBudW1iZXI7XHJcbiAgcGFnZV90aXRsZTogc3RyaW5nO1xyXG4gIHZpZGVvX2lkOiBzdHJpbmc7XHJcbiAgZ2VuZXJhdGlvbl9zdGF0dXM6IHN0cmluZztcclxuICB2aWRlb3N0YXR1c2FwaTogc3RyaW5nO1xyXG4gIGhleWdlbnZpZGVvdXJsOiBzdHJpbmc7XHJcbiAgaGV5Z2VuYmxvYnVybDogc3RyaW5nO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxuICBhdmF0YXJfbmFtZTogc3RyaW5nO1xyXG4gIHZlcnNpb25fbnVtYmVyOiBudW1iZXI7XHJcbn1cclxuXHJcblxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ291cnNlVmlld1BhZ2UoKSB7XHJcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgY291cnNlSWQgPSBwYXJhbXMuaWQgYXMgc3RyaW5nO1xyXG5cclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ092ZXJ2aWV3Jyk7XHJcbiAgY29uc3QgW3Nob3dBc3Nlc3NtZW50TW9kYWwsIHNldFNob3dBc3Nlc3NtZW50TW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjb3Vyc2UsIHNldENvdXJzZV0gPSB1c2VTdGF0ZTxDb3Vyc2UgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdmlkZW9zLCBzZXRWaWRlb3NdID0gdXNlU3RhdGU8Q291cnNlVmlkZW9bXT4oW10pO1xyXG4gIGNvbnN0IFtjdXJyZW50VmlkZW9JbmRleCwgc2V0Q3VycmVudFZpZGVvSW5kZXhdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbbGVmdFNpZGViYXJWaXNpYmxlLCBzZXRMZWZ0U2lkZWJhclZpc2libGVdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3JpZ2h0U2lkZWJhclZpc2libGUsIHNldFJpZ2h0U2lkZWJhclZpc2libGVdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2hhc1N0YXJ0ZWRDb3Vyc2UsIHNldEhhc1N0YXJ0ZWRDb3Vyc2VdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjb3Vyc2VQcm9ncmVzcywgc2V0Q291cnNlUHJvZ3Jlc3NdID0gdXNlU3RhdGU8YW55PihudWxsKTtcclxuICBjb25zdCBbbW9kdWxlUHJvZ3Jlc3MsIHNldE1vZHVsZVByb2dyZXNzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW2N1cnJlbnRVc2VyLCBzZXRDdXJyZW50VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFtpc1FuQU1vZGFsT3Blbiwgc2V0SXNRbkFNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuXHJcbiAgLy8gR2V0IHJlYWwgdXNlciBJRCBmcm9tIGF1dGhlbnRpY2F0aW9uIHRva2VuXHJcbiAgY29uc3QgZ2V0VXNlckZyb21Ub2tlbiA9ICgpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcbiAgICBpZiAodG9rZW4pIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBiYXNlNjRVcmwgPSB0b2tlbi5zcGxpdCgnLicpWzFdO1xyXG4gICAgICAgIGNvbnN0IGJhc2U2NCA9IGJhc2U2NFVybC5yZXBsYWNlKC8tL2csICcrJykucmVwbGFjZSgvXy9nLCAnLycpO1xyXG4gICAgICAgIGNvbnN0IGpzb25QYXlsb2FkID0gZGVjb2RlVVJJQ29tcG9uZW50KGF0b2IoYmFzZTY0KS5zcGxpdCgnJykubWFwKGZ1bmN0aW9uKGMpIHtcclxuICAgICAgICAgIHJldHVybiAnJScgKyAoJzAwJyArIGMuY2hhckNvZGVBdCgwKS50b1N0cmluZygxNikpLnNsaWNlKC0yKTtcclxuICAgICAgICB9KS5qb2luKCcnKSk7XHJcbiAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UoanNvblBheWxvYWQpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlY29kaW5nIHRva2VuOicsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfTtcclxuXHJcbiAgLy8gSW5pdGlhbGl6ZSB1c2VyIGZyb20gdG9rZW5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgdXNlciA9IGdldFVzZXJGcm9tVG9rZW4oKTtcclxuICAgIGlmICh1c2VyKSB7XHJcbiAgICAgIHNldEN1cnJlbnRVc2VyKHVzZXIpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gUmVkaXJlY3QgdG8gbG9naW4gaWYgbm8gdmFsaWQgdG9rZW5cclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL3NpZ25pbic7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCB1c2VySWQgPSBjdXJyZW50VXNlcj8uaWQ7XHJcblxyXG4gIC8vIEZldGNoIHVzZXIgcHJvZ3Jlc3NcclxuICBjb25zdCBmZXRjaFVzZXJQcm9ncmVzcyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIGlmICghdXNlcklkKSByZXR1cm47IC8vIERvbid0IGZldGNoIGlmIG5vIHVzZXIgSURcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBwcm9ncmVzc1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMS9hcGkvdXNlcnMvJHt1c2VySWR9L2NvdXJzZXMvJHtjb3Vyc2VJZH0vcHJvZ3Jlc3NgKTtcclxuICAgICAgaWYgKHByb2dyZXNzUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBwcm9ncmVzc0RhdGEgPSBhd2FpdCBwcm9ncmVzc1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBzZXRDb3Vyc2VQcm9ncmVzcyhwcm9ncmVzc0RhdGEuY291cnNlUHJvZ3Jlc3MpO1xyXG4gICAgICAgIHNldE1vZHVsZVByb2dyZXNzKHByb2dyZXNzRGF0YS5tb2R1bGVQcm9ncmVzcyk7XHJcblxyXG4gICAgICAgIC8vIFNldCBjdXJyZW50IHZpZGVvIGluZGV4IGJhc2VkIG9uIHByb2dyZXNzXHJcbiAgICAgICAgaWYgKHByb2dyZXNzRGF0YS5jb3Vyc2VQcm9ncmVzcyAmJiBwcm9ncmVzc0RhdGEuY291cnNlUHJvZ3Jlc3MuY3VycmVudF9tb2R1bGUgPiAxKSB7XHJcbiAgICAgICAgICBzZXRDdXJyZW50VmlkZW9JbmRleChwcm9ncmVzc0RhdGEuY291cnNlUHJvZ3Jlc3MuY3VycmVudF9tb2R1bGUgLSAxKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHByb2dyZXNzOicsIGVycm9yKTtcclxuICAgIH1cclxuICB9LCBbdXNlcklkLCBjb3Vyc2VJZF0pO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBzdGFydCBjb3Vyc2UgKHRyYWNrIGNvdXJzZSBlbnJvbGxtZW50KVxyXG4gIGNvbnN0IHN0YXJ0Q291cnNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCF1c2VySWQpIHJldHVybjsgLy8gRG9uJ3Qgc3RhcnQgaWYgbm8gdXNlciBJRFxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMS9hcGkvY291cnNlcy8ke2NvdXJzZUlkfS9zdGFydGAsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyB1c2VySWQgfSksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgc2V0SGFzU3RhcnRlZENvdXJzZSh0cnVlKTtcclxuICAgICAgICBhd2FpdCBmZXRjaFVzZXJQcm9ncmVzcygpOyAvLyBSZWZyZXNoIHByb2dyZXNzIGRhdGFcclxuICAgICAgICBjb25zb2xlLmxvZygnQ291cnNlIHN0YXJ0ZWQgZm9yIGNvdXJzZSBJRDonLCBjb3Vyc2VJZCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0YXJ0aW5nIGNvdXJzZTonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfSwgW3VzZXJJZCwgY291cnNlSWQsIGZldGNoVXNlclByb2dyZXNzXSk7XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIHVwZGF0ZSBtb2R1bGUgcHJvZ3Jlc3NcclxuICBjb25zdCB1cGRhdGVNb2R1bGVQcm9ncmVzcyA9IHVzZUNhbGxiYWNrKGFzeW5jIChtb2R1bGVOdW1iZXI6IG51bWJlciwgd2F0Y2hUaW1lOiBudW1iZXIsIHRvdGFsRHVyYXRpb246IG51bWJlciwgaXNDb21wbGV0ZWQ6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xyXG4gICAgaWYgKCF1c2VySWQpIHJldHVybjsgLy8gRG9uJ3QgdXBkYXRlIGlmIG5vIHVzZXIgSURcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBjdXJyZW50VmlkZW8gPSB2aWRlb3NbbW9kdWxlTnVtYmVyIC0gMV07XHJcbiAgICAgIGNvbnN0IHJlcXVlc3REYXRhID0ge1xyXG4gICAgICAgIHVzZXJJZCxcclxuICAgICAgICB3YXRjaFRpbWUsXHJcbiAgICAgICAgdG90YWxEdXJhdGlvbixcclxuICAgICAgICBpc0NvbXBsZXRlZCxcclxuICAgICAgICBtb2R1bGVUaXRsZTogY3VycmVudFZpZGVvPy5wYWdlX3RpdGxlIHx8IGBNb2R1bGUgJHttb2R1bGVOdW1iZXJ9YCxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyBtb2R1bGUgcHJvZ3Jlc3M6JywgcmVxdWVzdERhdGEpO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cDovL2xvY2FsaG9zdDo1MDAxL2FwaS9jb3Vyc2VzLyR7Y291cnNlSWR9L21vZHVsZXMvJHttb2R1bGVOdW1iZXJ9L3Byb2dyZXNzYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0RGF0YSksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdQcm9ncmVzcyB1cGRhdGUgc3VjY2Vzc2Z1bDonLCByZXN1bHQpO1xyXG4gICAgICAgIGF3YWl0IGZldGNoVXNlclByb2dyZXNzKCk7IC8vIFJlZnJlc2ggcHJvZ3Jlc3MgZGF0YVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdQcm9ncmVzcyB1cGRhdGUgZmFpbGVkOicsIGVycm9yRGF0YSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIG1vZHVsZSBwcm9ncmVzczonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfSwgW3VzZXJJZCwgY291cnNlSWQsIHZpZGVvcywgZmV0Y2hVc2VyUHJvZ3Jlc3NdKTtcclxuXHJcblxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hDb3Vyc2VEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcblxyXG4gICAgICAgIC8vIEZldGNoIGNvdXJzZSBkZXRhaWxzXHJcbiAgICAgICAgY29uc3QgY291cnNlUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cDovL2xvY2FsaG9zdDo1MDAxL2FwaS9jb3Vyc2VzLyR7Y291cnNlSWR9YCk7XHJcbiAgICAgICAgaWYgKCFjb3Vyc2VSZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY291cnNlIGRldGFpbHMnKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgY291cnNlRGF0YSA9IGF3YWl0IGNvdXJzZVJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBzZXRDb3Vyc2UoY291cnNlRGF0YSk7XHJcblxyXG4gICAgICAgIC8vIEZldGNoIGNvdXJzZSB2aWRlb3NcclxuICAgICAgICBjb25zdCB2aWRlb3NSZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDEvYXBpL2NvdXJzZXMvJHtjb3Vyc2VJZH0vdmlkZW9zYCk7XHJcbiAgICAgICAgaWYgKCF2aWRlb3NSZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY291cnNlIHZpZGVvcycpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCB2aWRlb3NEYXRhID0gYXdhaXQgdmlkZW9zUmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHNldFZpZGVvcyh2aWRlb3NEYXRhKTtcclxuXHJcbiAgICAgICAgLy8gRmV0Y2ggdXNlciBwcm9ncmVzcyBmb3IgdGhpcyBjb3Vyc2UgKG9ubHkgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkKVxyXG4gICAgICAgIGlmICh1c2VySWQpIHtcclxuICAgICAgICAgIGF3YWl0IGZldGNoVXNlclByb2dyZXNzKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgfSBjYXRjaCAoZXJyOiB1bmtub3duKSB7XHJcbiAgICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdBbiBlcnJvciBvY2N1cnJlZCcpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGlmIChjb3Vyc2VJZCAmJiB1c2VySWQpIHtcclxuICAgICAgZmV0Y2hDb3Vyc2VEYXRhKCk7XHJcbiAgICB9XHJcbiAgfSwgW2NvdXJzZUlkLCB1c2VySWRdKTtcclxuXHJcbiAgLy8gS2V5Ym9hcmQgbmF2aWdhdGlvblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVLZXlQcmVzcyA9IChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4ge1xyXG4gICAgICBpZiAodmlkZW9zLmxlbmd0aCA9PT0gMCkgcmV0dXJuO1xyXG5cclxuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ0Fycm93TGVmdCcgJiYgY3VycmVudFZpZGVvSW5kZXggPiAwKSB7XHJcbiAgICAgICAgc2V0Q3VycmVudFZpZGVvSW5kZXgoY3VycmVudFZpZGVvSW5kZXggLSAxKTtcclxuICAgICAgfSBlbHNlIGlmIChldmVudC5rZXkgPT09ICdBcnJvd1JpZ2h0JyAmJiBjdXJyZW50VmlkZW9JbmRleCA8IHZpZGVvcy5sZW5ndGggLSAxKSB7XHJcbiAgICAgICAgc2V0Q3VycmVudFZpZGVvSW5kZXgoY3VycmVudFZpZGVvSW5kZXggKyAxKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleVByZXNzKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleVByZXNzKTtcclxuICB9LCBbY3VycmVudFZpZGVvSW5kZXgsIHZpZGVvcy5sZW5ndGhdKTtcclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtaW4taC1zY3JlZW4gYmctWyNmYWZiZmNdXCI+XHJcbiAgICAgICAgPE5hdmJhciAvPlxyXG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtZ3JvdyBjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgY291cnNlLi4uPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L21haW4+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGlmIChlcnJvciB8fCAhY291cnNlKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuIGJnLVsjZmFmYmZjXVwiPlxyXG4gICAgICAgIDxOYXZiYXIgLz5cclxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LWdyb3cgY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtcmVkLTYwMFwiPlxyXG4gICAgICAgICAgICAgIHtlcnJvciB8fCAnQ291cnNlIG5vdCBmb3VuZCd9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9tYWluPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBjdXJyZW50VmlkZW8gPSB2aWRlb3NbY3VycmVudFZpZGVvSW5kZXhdO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XHJcbiAgICAgIDxOYXZiYXIgLz5cclxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtW2NhbGMoMTAwdmgtNjRweCldIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICB7LyogTGVmdCBTaWRlYmFyOiBDb3Vyc2UgQ3VycmljdWx1bSAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtsZWZ0U2lkZWJhclZpc2libGUgPyAndy04MCcgOiAndy0wJ30gYmctd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwIGZsZXggZmxleC1jb2wgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIG92ZXJmbG93LWhpZGRlbmB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyYXktNjAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0zIDRhMSAxIDAgMDExLTFoMTJhMSAxIDAgMDExIDF2MmExIDEgMCAwMS0xIDFINGExIDEgMCAwMS0xLTFWNHptMCA0YTEgMSAwIDAxMS0xaDZhMSAxIDAgMDExIDF2NmExIDEgMCAwMS0xIDFINGExIDEgMCAwMS0xLTFWOHptOCAwYTEgMSAwIDAxMS0xaDRhMSAxIDAgMDExIDF2MmExIDEgMCAwMS0xIDFoLTRhMSAxIDAgMDEtMS0xVjh6bTAgNGExIDEgMCAwMTEtMWg0YTEgMSAwIDAxMSAxdjJhMSAxIDAgMDEtMSAxaC00YTEgMSAwIDAxLTEtMXYtMnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5Db3Vyc2UgQ3VycmljdWx1bTwvaDI+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAge3ZpZGVvcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt2aWRlb3MubGVuZ3RofSBtb2R1bGVzPC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICAgICAge3ZpZGVvcy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTJcIj5cclxuICAgICAgICAgICAgICAgICAge3ZpZGVvcy5tYXAoKHZpZGVvLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1vZHVsZVByb2dyZXNzRGF0YSA9IG1vZHVsZVByb2dyZXNzLmZpbmQobXAgPT4gbXAubW9kdWxlX251bWJlciA9PT0gdmlkZW8ucGFnZV9udW1iZXIpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQ29tcGxldGVkID0gbW9kdWxlUHJvZ3Jlc3NEYXRhPy5pc19jb21wbGV0ZWQgfHwgZmFsc2U7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29tcGxldGlvblBlcmNlbnRhZ2UgPSBtb2R1bGVQcm9ncmVzc0RhdGE/LmNvbXBsZXRpb25fcGVyY2VudGFnZSB8fCAwO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3ZpZGVvLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgbWItMiByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID09PSBjdXJyZW50VmlkZW9JbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWdyYXktNTAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtpc0NvbXBsZXRlZCA/ICdiZy1ncmVlbi01MCcgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFZpZGVvSW5kZXgoaW5kZXgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaGFzU3RhcnRlZENvdXJzZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRDb3Vyc2UoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC1zaHJpbmstMCB3LTggaC04IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzQ29tcGxldGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGluZGV4ID09PSBjdXJyZW50VmlkZW9JbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNTAwIHRleHQtd2hpdGUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNDb21wbGV0ZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGluZGV4ID09PSBjdXJyZW50VmlkZW9JbmRleCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6TTkuNTU1IDcuMTY4QTEgMSAwIDAwOCA4djRhMSAxIDAgMDAxLjU1NS44MzJsMy0yYTEgMSAwIDAwMC0xLjY2NGwtMy0yelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlkZW8ucGFnZV9udW1iZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtIHRleHQtc20gbGVhZGluZy10aWdodCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBsZXRlZCA/ICd0ZXh0LWdyZWVuLTgwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gY3VycmVudFZpZGVvSW5kZXggPyAndGV4dC1ibHVlLTgwMCcgOiAndGV4dC1ncmF5LTgwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTW9kdWxlIHt2aWRlby5wYWdlX251bWJlcn06IHt2aWRlby5wYWdlX3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQteHMgbXQtMSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBsZXRlZCA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PT0gY3VycmVudFZpZGVvSW5kZXggPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC1ncmF5LTUwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZpZGVvLmF2YXRhcl9uYW1lfSDigKIgMzAgbWluXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC14cyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzQ29tcGxldGVkID8gJ3RleHQtZ3JlZW4tNjAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IGN1cnJlbnRWaWRlb0luZGV4ID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNDb21wbGV0ZWQgPyAnQ29tcGxldGVkJyA6IGNvbXBsZXRpb25QZXJjZW50YWdlID4gMCA/IGAke01hdGgucm91bmQoY29tcGxldGlvblBlcmNlbnRhZ2UpfSUgd2F0Y2hlZGAgOiAnTm90IHN0YXJ0ZWQnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb21wbGV0aW9uUGVyY2VudGFnZSA+IDAgJiYgIWlzQ29tcGxldGVkICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGgtMSByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke2NvbXBsZXRpb25QZXJjZW50YWdlfSVgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgfSl9XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogVGFrZSBGaW5hbCBBc3Nlc3NtZW50ICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbXQtNCBwLTMgcm91bmRlZC1sZyBib3JkZXIgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzPy5wcm9ncmVzc19wZXJjZW50YWdlID09PSAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCBob3ZlcjpiZy1ncmVlbi0xMDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktNTAgYm9yZGVyLWdyYXktMjAwIGN1cnNvci1ub3QtYWxsb3dlZCBvcGFjaXR5LTYwJ1xyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGlmIChjb3Vyc2VQcm9ncmVzcz8ucHJvZ3Jlc3NfcGVyY2VudGFnZSA9PT0gMTAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dBc3Nlc3NtZW50TW9kYWwodHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy04IGgtOCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY291cnNlUHJvZ3Jlc3M/LnByb2dyZXNzX3BlcmNlbnRhZ2UgPT09IDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCdcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9e2B3LTQgaC00ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY291cnNlUHJvZ3Jlc3M/LnByb2dyZXNzX3BlcmNlbnRhZ2UgPT09IDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0gZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSB0ZXh0LXNtICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY291cnNlUHJvZ3Jlc3M/LnByb2dyZXNzX3BlcmNlbnRhZ2UgPT09IDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi04MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNTAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+VGFrZSBGaW5hbCBBc3Nlc3NtZW50PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC14cyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZVByb2dyZXNzPy5wcm9ncmVzc19wZXJjZW50YWdlID09PSAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtZ3JlZW4tNjAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTQwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VQcm9ncmVzcz8ucHJvZ3Jlc3NfcGVyY2VudGFnZSA9PT0gMTAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdDbGljayB0byBzdGFydCBhc3Nlc3NtZW50J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnQ29tcGxldGUgYWxsIG1vZHVsZXMgdG8gdW5sb2NrIGFzc2Vzc21lbnQnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8cD5ObyBjdXJyaWN1bHVtIGF2YWlsYWJsZTwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIExlZnQgU2lkZWJhciBUb2dnbGUgQnV0dG9uICovfVxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRMZWZ0U2lkZWJhclZpc2libGUoIWxlZnRTaWRlYmFyVmlzaWJsZSl9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHotMTAgYmcteWVsbG93LTQwMCBob3ZlcjpiZy15ZWxsb3ctNTAwIHRleHQtYmxhY2sgcC0yIHJvdW5kZWQtci1sZyBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgc3R5bGU9e3sgbGVmdDogbGVmdFNpZGViYXJWaXNpYmxlID8gJzMyMHB4JyA6ICcwcHgnIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTUgaC01IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCAke2xlZnRTaWRlYmFyVmlzaWJsZSA/ICdyb3RhdGUtMTgwJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMCAyMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTcuMjkzIDE0LjcwN2ExIDEgMCAwMTAtMS40MTRMMTAuNTg2IDEwIDcuMjkzIDYuNzA3YTEgMSAwIDAxMS40MTQtMS40MTRsNCA0YTEgMSAwIDAxMCAxLjQxNGwtNCA0YTEgMSAwIDAxLTEuNDE0IDB6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICB7LyogQ2VudGVyOiBWaWRlbyBQbGF5ZXIgYW5kIENvbnRlbnQgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgIHsvKiBWaWRlbyBQbGF5ZXIgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2sgcmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICB7dmlkZW9zLmxlbmd0aCA+IDAgJiYgY3VycmVudFZpZGVvICYmIGN1cnJlbnRWaWRlby5oZXlnZW5ibG9idXJsID8gKFxyXG4gICAgICAgICAgICAgICAgPHZpZGVvXHJcbiAgICAgICAgICAgICAgICAgIGtleT17Y3VycmVudFZpZGVvLmhleWdlbmJsb2J1cmx9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLVs0MDBweF0gb2JqZWN0LWNvdmVyXCJcclxuICAgICAgICAgICAgICAgICAgY29udHJvbHNcclxuICAgICAgICAgICAgICAgICAgYXV0b1BsYXlcclxuICAgICAgICAgICAgICAgICAgb25QbGF5PXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFoYXNTdGFydGVkQ291cnNlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzdGFydENvdXJzZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgb25UaW1lVXBkYXRlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZpZGVvID0gZS50YXJnZXQgYXMgSFRNTFZpZGVvRWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB3YXRjaFRpbWUgPSBNYXRoLmZsb29yKHZpZGVvLmN1cnJlbnRUaW1lKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0b3RhbER1cmF0aW9uID0gTWF0aC5mbG9vcih2aWRlby5kdXJhdGlvbik7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSBwcm9ncmVzcyBldmVyeSAxMCBzZWNvbmRzXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHdhdGNoVGltZSAlIDEwID09PSAwICYmIHdhdGNoVGltZSA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZU1vZHVsZVByb2dyZXNzKGN1cnJlbnRWaWRlby5wYWdlX251bWJlciwgd2F0Y2hUaW1lLCB0b3RhbER1cmF0aW9uKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIG9uRW5kZWQ9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2aWRlbyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ3ZpZGVvJykgYXMgSFRNTFZpZGVvRWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICBpZiAodmlkZW8pIHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvdGFsRHVyYXRpb24gPSBNYXRoLmZsb29yKHZpZGVvLmR1cmF0aW9uKTtcclxuICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZU1vZHVsZVByb2dyZXNzKGN1cnJlbnRWaWRlby5wYWdlX251bWJlciwgdG90YWxEdXJhdGlvbiwgdG90YWxEdXJhdGlvbiwgdHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyBBdXRvLWFkdmFuY2UgdG8gbmV4dCB2aWRlbyBhZnRlciBhIHNob3J0IGRlbGF5XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoY3VycmVudFZpZGVvSW5kZXggPCB2aWRlb3MubGVuZ3RoIC0gMSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDdXJyZW50VmlkZW9JbmRleChjdXJyZW50VmlkZW9JbmRleCArIDEpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0sIDE1MDApOyAvLyAxLjUgc2Vjb25kIGRlbGF5IGJlZm9yZSBhdXRvLWFkdmFuY2luZ1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8c291cmNlIHNyYz17Y3VycmVudFZpZGVvLmhleWdlbmJsb2J1cmx9IHR5cGU9XCJ2aWRlby9tcDRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICBZb3VyIGJyb3dzZXIgZG9lcyBub3Qgc3VwcG9ydCB0aGUgdmlkZW8gdGFnLlxyXG4gICAgICAgICAgICAgICAgPC92aWRlbz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1bNDAwcHhdIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIHt2aWRlb3MubGVuZ3RoID09PSAwID8gJ05vIHZpZGVvcyBhdmFpbGFibGUgZm9yIHRoaXMgY291cnNlJyA6ICdMb2FkaW5nIHZpZGVvLi4uJ31cclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgey8qIEhhbmQgUmFpc2UgSWNvbiBPdmVybGF5ICovfVxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzUW5BTW9kYWxPcGVuKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCBiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHAtMyByb3VuZGVkLWZ1bGwgc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTEwIGdyb3VwIHotMTBcIlxyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJSYWlzZSBIYW5kXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgZ3JvdXAtaG92ZXI6YW5pbWF0ZS1ib3VuY2VcIlxyXG4gICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMCAyMFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNOSAzYTEgMSAwIDAxMiAwdjUuNWEuNS41IDAgMDAxIDBWNGExIDEgMCAxMTIgMHY0LjVhLjUuNSAwIDAwMSAwVjZhMSAxIDAgMTEyIDB2NmE3IDcgMCAxMS0xNCAwVjlhMSAxIDAgMDEyIDB2Mi41YS41LjUgMCAwMDEgMFY0YTEgMSAwIDAxMiAwdjQuNWEuNS41IDAgMDAxIDBWM3pcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIi8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogVGFicyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC04IHB4LTZcIj5cclxuICAgICAgICAgICAgICAgIHtbJ092ZXJ2aWV3JywgJ0Fzc2Vzc21lbnQnLCAnRGlzY3Vzc2lvbicsICdSZXZpZXdzJ10ubWFwKCh0YWIpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17dGFifVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTQgZm9udC1tZWRpdW0gYm9yZGVyLWItMiB0cmFuc2l0aW9uLWNvbG9ycyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIgJiYgdGFiICE9PSAnQXNzZXNzbWVudCcgJiYgdGFiICE9PSAnRGlzY3Vzc2lvbidcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLW9yYW5nZS01MDAgdGV4dC1vcmFuZ2UtNjAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwJ1xyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGlmICh0YWIgPT09ICdBc3Nlc3NtZW50Jykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93QXNzZXNzbWVudE1vZGFsKHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0YWIgPT09ICdEaXNjdXNzaW9uJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChgL2NvdXJzZXMvJHtjb3Vyc2VJZH0vZGlzY3Vzc2lvbmApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0QWN0aXZlVGFiKHRhYik7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIHt0YWJ9XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFRhYiBDb250ZW50ICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBiZy13aGl0ZSBwLTYgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ092ZXJ2aWV3JyAmJiAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj5BYm91dCBUaGlzIENvdXJzZTwvaDI+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIENvbXByZWhlbnNpdmUgdHJhaW5pbmcgaW4gY2FyZGlvdmFzY3VsYXIgbWVkaWNpbmUgY292ZXJpbmcgZGlhZ25vc2lzLCB0cmVhdG1lbnQsIGFuZCBwYXRpZW50IGNhcmUgcHJvdG9jb2xzLlxyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLThcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0zXCI+V2hhdCB5b3UnbGwgbGVhcm48L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDAgbXQtMVwiPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBJbmR1c3RyeSBiZXN0IHByYWN0aWNlcyBhbmQgc3RhbmRhcmRzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDAgbXQtMVwiPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcmFjdGljYWwgaW1wbGVtZW50YXRpb24gc3RyYXRlZ2llc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIG10LTFcIj7igKI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVhbC13b3JsZCBjYXNlIHN0dWRpZXMgYW5kIGV4YW1wbGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDAgbXQtMVwiPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcm9mZXNzaW9uYWwgY2VydGlmaWNhdGlvbiBwcmVwYXJhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItM1wiPlByZXJlcXVpc2l0ZXM8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFkdmFuY2VkIGtub3dsZWRnZSBhbmQgZXhwZXJpZW5jZSByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItM1wiPkNvdXJzZSBEZXRhaWxzPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTEgdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGk+RG9tYWluOiB7Y291cnNlLmRvbWFpbn08L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGk+TGV2ZWw6IHtjb3Vyc2UubGV2ZWx9PC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPkR1cmF0aW9uOiB7Y291cnNlLmVzdGltYXRlZF9ob3Vyc30gaG91cnM8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGk+TW9kdWxlczoge3ZpZGVvcy5sZW5ndGh9PC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPkNlcnRpZmljYXRlOiBBdmFpbGFibGUgdXBvbiBjb21wbGV0aW9uPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcblxyXG5cclxuXHJcblxyXG4gICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdSZXZpZXdzJyAmJiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgcHktOFwiPlN0dWRlbnQgcmV2aWV3cyBjb21pbmcgc29vbi4uLjwvZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFJpZ2h0IFNpZGViYXIgVG9nZ2xlIEJ1dHRvbiAqL31cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UmlnaHRTaWRlYmFyVmlzaWJsZSghcmlnaHRTaWRlYmFyVmlzaWJsZSl9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB6LTEwIGJnLXllbGxvdy00MDAgaG92ZXI6YmcteWVsbG93LTUwMCB0ZXh0LWJsYWNrIHAtMiByb3VuZGVkLWwtbGcgc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgIHN0eWxlPXt7IHJpZ2h0OiByaWdodFNpZGViYXJWaXNpYmxlID8gJzMyMHB4JyA6ICcwcHgnIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTUgaC01IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCAke3JpZ2h0U2lkZWJhclZpc2libGUgPyAnJyA6ICdyb3RhdGUtMTgwJ31gfVxyXG4gICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjAgMjBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk03LjI5MyAxNC43MDdhMSAxIDAgMDEwLTEuNDE0TDEwLjU4NiAxMCA3LjI5MyA2LjcwN2ExIDEgMCAwMTEuNDE0LTEuNDE0bDQgNGExIDEgMCAwMTAgMS40MTRsLTQgNGExIDEgMCAwMS0xLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgey8qIFJpZ2h0IFNpZGViYXI6IENvdXJzZSBJbmZvICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Ake3JpZ2h0U2lkZWJhclZpc2libGUgPyAndy04MCcgOiAndy0wJ30gYmctd2hpdGUgYm9yZGVyLWwgYm9yZGVyLWdyYXktMjAwIGZsZXggZmxleC1jb2wgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIG92ZXJmbG93LWhpZGRlbmB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIG1iLTJcIj5Db3Vyc2UgSW5mbzwvaDM+XHJcbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItM1wiPntjb3Vyc2UudGl0bGV9PC9oMj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICB7Y291cnNlLmRvbWFpbn1cclxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC15ZWxsb3ctNDAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTkuMDQ5IDIuOTI3Yy4zLS45MjEgMS42MDMtLjkyMSAxLjkwMiAwbDEuMDcgMy4yOTJhMSAxIDAgMDAuOTUuNjloMy40NjJjLjk2OSAwIDEuMzcxIDEuMjQuNTg4IDEuODFsLTIuOCAyLjAzNGExIDEgMCAwMC0uMzY0IDEuMTE4bDEuMDcgMy4yOTJjLjMuOTIxLS43NTUgMS42ODgtMS41NCAxLjExOGwtMi44LTIuMDM0YTEgMSAwIDAwLTEuMTc1IDBsLTIuOCAyLjAzNGMtLjc4NC41Ny0xLjgzOC0uMTk3LTEuNTM5LTEuMTE4bDEuMDctMy4yOTJhMSAxIDAgMDAtLjM2NC0xLjExOEwyLjk4IDguNzJjLS43ODMtLjU3LS4zOC0xLjgxLjU4OC0xLjgxaDMuNDYxYTEgMSAwIDAwLjk1MS0uNjlsMS4wNy0zLjI5MnpcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj40Ljg8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPigyMzQgcmV2aWV3cyk8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTEtMTJhMSAxIDAgMTAtMiAwdjRhMSAxIDAgMDAuMjkzLjcwN2wyLjgyOCAyLjgyOWExIDEgMCAxMDEuNDE1LTEuNDE1TDExIDkuNTg2VjZ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2UuZXN0aW1hdGVkX2hvdXJzfSBob3Vyczwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTMgNmEzIDMgMCAxMS02IDAgMyAzIDAgMDE2IDB6TTE4IDhhMiAyIDAgMTEtNCAwIDIgMiAwIDAxNCAwek0xNCAxNWE0IDQgMCAwMC04IDB2M2g4di0zelwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57dmlkZW9zLmxlbmd0aH0gc3R1ZGVudHM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTMgNGExIDEgMCAwMTEtMWgxMmExIDEgMCAwMTEgMXYyYTEgMSAwIDAxLTEgMUg0YTEgMSAwIDAxLTEtMVY0em0wIDRhMSAxIDAgMDExLTFoNmExIDEgMCAwMTEgMXY2YTEgMSAwIDAxLTEgMUg0YTEgMSAwIDAxLTEtMVY4em04IDBhMSAxIDAgMDExLTFoNGExIDEgMCAwMTEgMXYyYTEgMSAwIDAxLTEgMWgtNGExIDEgMCAwMS0xLTFWOHptMCA0YTEgMSAwIDAxMS0xaDRhMSAxIDAgMDExIDF2MmExIDEgMCAwMS0xIDFoLTRhMSAxIDAgMDEtMS0xdi0yelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57Y291cnNlLmxldmVsfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNi4yNjcgMy40NTVhMy4wNjYgMy4wNjYgMCAwMDEuNzQ1LS43MjMgMy4wNjYgMy4wNjYgMCAwMTMuOTc2IDAgMy4wNjYgMy4wNjYgMCAwMDEuNzQ1LjcyMyAzLjA2NiAzLjA2NiAwIDAxMi44MTIgMi44MTJjLjA1MS42NDMuMzA0IDEuMjU0LjcyMyAxLjc0NWEzLjA2NiAzLjA2NiAwIDAxMCAzLjk3NiAzLjA2NiAzLjA2NiAwIDAwLS43MjMgMS43NDUgMy4wNjYgMy4wNjYgMCAwMS0yLjgxMiAyLjgxMiAzLjA2NiAzLjA2NiAwIDAwLTEuNzQ1LjcyMyAzLjA2NiAzLjA2NiAwIDAxLTMuOTc2IDAgMy4wNjYgMy4wNjYgMCAwMC0xLjc0NS0uNzIzIDMuMDY2IDMuMDY2IDAgMDEtMi44MTItMi44MTIgMy4wNjYgMy4wNjYgMCAwMC0uNzIzLTEuNzQ1IDMuMDY2IDMuMDY2IDAgMDEwLTMuOTc2IDMuMDY2IDMuMDY2IDAgMDAuNzIzLTEuNzQ1IDMuMDY2IDMuMDY2IDAgMDEyLjgxMi0yLjgxMnptNy40NCA1LjI1MmExIDEgMCAwMC0xLjQxNC0xLjQxNEw5IDEwLjU4NiA3LjcwNyA5LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNGwyIDJhMSAxIDAgMDAxLjQxNCAwbDQtNHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+Q2VydGlmaWNhdGU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIDxidXR0b25cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1vcmFuZ2UtNTAwIGhvdmVyOmJnLW9yYW5nZS02MDAgdGV4dC13aGl0ZSBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCBtYi0zIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgaWYgKCFoYXNTdGFydGVkQ291cnNlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc3RhcnRDb3Vyc2UoKTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBTdGFydCBDb3Vyc2VcclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWdyYXktNzAwIHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzXCI+XHJcbiAgICAgICAgICAgICAgICBBZGQgdG8gV2lzaGxpc3RcclxuICAgICAgICAgICAgICA8L2J1dHRvbj4gKi99XHJcblxyXG5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogQ3VycmVudCBQcm9ncmVzcyAqL31cclxuICAgICAgICAgICAge3ZpZGVvcy5sZW5ndGggPiAwICYmIGNvdXJzZVByb2dyZXNzICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBtYi0zXCI+Q291cnNlIFByb2dyZXNzPC9oND5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCByb3VuZGVkLWxnIHAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+T3ZlcmFsbCBQcm9ncmVzczwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1vcmFuZ2UtNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7TWF0aC5yb3VuZChjb3Vyc2VQcm9ncmVzcy5wcm9ncmVzc19wZXJjZW50YWdlKX0lXHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMiBtYi0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctb3JhbmdlLTUwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7Y291cnNlUHJvZ3Jlc3MucHJvZ3Jlc3NfcGVyY2VudGFnZX0lYCB9fVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8cD57Y291cnNlUHJvZ3Jlc3MuY29tcGxldGVkX21vZHVsZXN9IG9mIHtjb3Vyc2VQcm9ncmVzcy50b3RhbF9tb2R1bGVzfSBtb2R1bGVzIGNvbXBsZXRlZDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cD5TdGF0dXM6IDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VQcm9ncmVzcy5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ3RleHQtZ3JlZW4tNjAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VQcm9ncmVzcy5zdGF0dXMgPT09ICdpbl9wcm9ncmVzcycgPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC1ncmF5LTYwMCdcclxuICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlUHJvZ3Jlc3Muc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdDb21wbGV0ZWQnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VQcm9ncmVzcy5zdGF0dXMgPT09ICdpbl9wcm9ncmVzcycgPyAnSW4gUHJvZ3Jlc3MnIDogJ05vdCBTdGFydGVkJ31cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VQcm9ncmVzcy5zdGFydGVkX2F0ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxwPlN0YXJ0ZWQ6IHtuZXcgRGF0ZShjb3Vyc2VQcm9ncmVzcy5zdGFydGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICB7Y291cnNlUHJvZ3Jlc3MuY29tcGxldGVkX2F0ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxwPkNvbXBsZXRlZDoge25ldyBEYXRlKGNvdXJzZVByb2dyZXNzLmNvbXBsZXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIEN1cnJlbnQgTW9kdWxlIEluZm8gKi99XHJcbiAgICAgICAgICAgICAgICB7Y3VycmVudFZpZGVvICYmIChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHAtMyBiZy1ibHVlLTUwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtODAwIG1iLTFcIj5DdXJyZW50bHkgV2F0Y2hpbmc8L2g1PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgTW9kdWxlIHtjdXJyZW50VmlkZW8ucGFnZV9udW1iZXJ9OiB7Y3VycmVudFZpZGVvLnBhZ2VfdGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVudFZpZGVvLmF2YXRhcl9uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvbWFpbj5cclxuXHJcbiAgICAgIHsvKiBRbkEgd2l0aCBIZXlHZW4gTW9kYWwgKi99XHJcbiAgICAgIDxRbkFXaXRoSGV5R2VuTW9kYWxcclxuICAgICAgICBpc09wZW49e2lzUW5BTW9kYWxPcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzUW5BTW9kYWxPcGVuKGZhbHNlKX1cclxuICAgICAgICBjb3Vyc2VJZD17Y291cnNlSWR9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7LyogQXNzZXNzbWVudCBNb2RhbCAqL31cclxuICAgICAgPEFzc2Vzc21lbnRNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17c2hvd0Fzc2Vzc21lbnRNb2RhbH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93QXNzZXNzbWVudE1vZGFsKGZhbHNlKX1cclxuICAgICAgICBjb3Vyc2VJZD17Y291cnNlSWR9XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIk5hdmJhciIsIlFuQVdpdGhIZXlHZW5Nb2RhbCIsIkFzc2Vzc21lbnRNb2RhbCIsIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVBhcmFtcyIsInVzZVJvdXRlciIsIkNvdXJzZVZpZXdQYWdlIiwicGFyYW1zIiwicm91dGVyIiwiY291cnNlSWQiLCJpZCIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInNob3dBc3Nlc3NtZW50TW9kYWwiLCJzZXRTaG93QXNzZXNzbWVudE1vZGFsIiwiY291cnNlIiwic2V0Q291cnNlIiwidmlkZW9zIiwic2V0VmlkZW9zIiwiY3VycmVudFZpZGVvSW5kZXgiLCJzZXRDdXJyZW50VmlkZW9JbmRleCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImxlZnRTaWRlYmFyVmlzaWJsZSIsInNldExlZnRTaWRlYmFyVmlzaWJsZSIsInJpZ2h0U2lkZWJhclZpc2libGUiLCJzZXRSaWdodFNpZGViYXJWaXNpYmxlIiwiaGFzU3RhcnRlZENvdXJzZSIsInNldEhhc1N0YXJ0ZWRDb3Vyc2UiLCJjb3Vyc2VQcm9ncmVzcyIsInNldENvdXJzZVByb2dyZXNzIiwibW9kdWxlUHJvZ3Jlc3MiLCJzZXRNb2R1bGVQcm9ncmVzcyIsImN1cnJlbnRVc2VyIiwic2V0Q3VycmVudFVzZXIiLCJpc1FuQU1vZGFsT3BlbiIsInNldElzUW5BTW9kYWxPcGVuIiwiZ2V0VXNlckZyb21Ub2tlbiIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImJhc2U2NFVybCIsInNwbGl0IiwiYmFzZTY0IiwicmVwbGFjZSIsImpzb25QYXlsb2FkIiwiZGVjb2RlVVJJQ29tcG9uZW50IiwiYXRvYiIsIm1hcCIsImMiLCJjaGFyQ29kZUF0IiwidG9TdHJpbmciLCJzbGljZSIsImpvaW4iLCJKU09OIiwicGFyc2UiLCJjb25zb2xlIiwidXNlciIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsInVzZXJJZCIsImZldGNoVXNlclByb2dyZXNzIiwicHJvZ3Jlc3NSZXNwb25zZSIsImZldGNoIiwib2siLCJwcm9ncmVzc0RhdGEiLCJqc29uIiwiY3VycmVudF9tb2R1bGUiLCJzdGFydENvdXJzZSIsInJlc3BvbnNlIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJzdHJpbmdpZnkiLCJsb2ciLCJ1cGRhdGVNb2R1bGVQcm9ncmVzcyIsIm1vZHVsZU51bWJlciIsIndhdGNoVGltZSIsInRvdGFsRHVyYXRpb24iLCJpc0NvbXBsZXRlZCIsImN1cnJlbnRWaWRlbyIsInJlcXVlc3REYXRhIiwibW9kdWxlVGl0bGUiLCJwYWdlX3RpdGxlIiwicmVzdWx0IiwiZXJyb3JEYXRhIiwiZmV0Y2hDb3Vyc2VEYXRhIiwiY291cnNlUmVzcG9uc2UiLCJFcnJvciIsImNvdXJzZURhdGEiLCJ2aWRlb3NSZXNwb25zZSIsInZpZGVvc0RhdGEiLCJlcnIiLCJtZXNzYWdlIiwiaGFuZGxlS2V5UHJlc3MiLCJldmVudCIsImxlbmd0aCIsImtleSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwiaDIiLCJwIiwidmlkZW8iLCJpbmRleCIsIm1vZHVsZVByb2dyZXNzRGF0YSIsImZpbmQiLCJtcCIsIm1vZHVsZV9udW1iZXIiLCJwYWdlX251bWJlciIsImlzX2NvbXBsZXRlZCIsImNvbXBsZXRpb25QZXJjZW50YWdlIiwiY29tcGxldGlvbl9wZXJjZW50YWdlIiwib25DbGljayIsImgzIiwiYXZhdGFyX25hbWUiLCJNYXRoIiwicm91bmQiLCJzdHlsZSIsIndpZHRoIiwicHJvZ3Jlc3NfcGVyY2VudGFnZSIsImJ1dHRvbiIsImxlZnQiLCJoZXlnZW5ibG9idXJsIiwiY29udHJvbHMiLCJhdXRvUGxheSIsIm9uUGxheSIsIm9uVGltZVVwZGF0ZSIsImUiLCJ0YXJnZXQiLCJmbG9vciIsImN1cnJlbnRUaW1lIiwiZHVyYXRpb24iLCJvbkVuZGVkIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwic2V0VGltZW91dCIsInNvdXJjZSIsInNyYyIsInR5cGUiLCJzcGFuIiwidGl0bGUiLCJ0YWIiLCJwdXNoIiwidWwiLCJsaSIsImRvbWFpbiIsImxldmVsIiwiZXN0aW1hdGVkX2hvdXJzIiwicmlnaHQiLCJoNCIsImNvbXBsZXRlZF9tb2R1bGVzIiwidG90YWxfbW9kdWxlcyIsInN0YXR1cyIsInN0YXJ0ZWRfYXQiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiY29tcGxldGVkX2F0IiwiaDUiLCJpc09wZW4iLCJvbkNsb3NlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/courses/[id]/page.tsx\n"));

/***/ })

});